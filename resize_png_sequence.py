#!/usr/bin/env python3
"""
PNG Sequence Resizer
Utility to resize PNG sequence files and overwrite original files
"""

import os
import argparse
import logging
from pathlib import Path
from PIL import Image
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def resize_png_sequence_files(
    png_sequence_folder: str,
    new_width: int,
    new_height: int,
    backup: bool = True,
    quality: str = 'bilinear'
) -> None:
    """
    Resize PNG sequence files and overwrite the original files
    
    Args:
        png_sequence_folder: Path to PNG sequence folder
        new_width: New width in pixels
        new_height: New height in pixels
        backup: Whether to create backup of original files (default: True)
        quality: Resize quality ('bilinear', 'bicubic', 'lanczos', 'nearest')
    
    Raises:
        IOError: If folder doesn't exist or no PNG files found
        ValueError: If invalid quality parameter
    """
    folder_path = Path(png_sequence_folder)
    
    if not folder_path.exists() or not folder_path.is_dir():
        raise IOError(f"PNG sequence folder does not exist or is not a directory: {png_sequence_folder}")
    
    # Find all PNG files matching frame_xxxxxx.png pattern
    png_files = []
    for file_path in folder_path.glob("frame_*.png"):
        if file_path.name.lower().endswith('.png'):
            png_files.append(file_path)
    
    if not png_files:
        raise IOError(f"No PNG files matching frame_xxxxxx.png pattern found in: {png_sequence_folder}")
    
    # Sort files by name to ensure correct order
    png_files.sort(key=lambda x: x.name)
    
    # Map quality string to PIL resampling filter
    quality_map = {
        'nearest': Image.Resampling.NEAREST,
        'bilinear': Image.Resampling.BILINEAR,
        'bicubic': Image.Resampling.BICUBIC,
        'lanczos': Image.Resampling.LANCZOS
    }
    
    if quality.lower() not in quality_map:
        raise ValueError(f"Invalid quality parameter: {quality}. Must be one of: {list(quality_map.keys())}")
    
    resample_filter = quality_map[quality.lower()]
    
    # Create backup folder if requested
    backup_folder = None
    if backup:
        backup_folder = folder_path / "backup_original"
        backup_folder.mkdir(exist_ok=True)
        logger.info(f"Created backup folder: {backup_folder}")
    
    processed_count = 0
    failed_count = 0
    
    logger.info(f"Starting to resize {len(png_files)} PNG files to {new_width}x{new_height}")
    
    for png_file in png_files:
        try:
            # Read original image
            with Image.open(png_file) as image:
                original_size = image.size
                
                # Create backup if requested
                if backup:
                    backup_path = backup_folder / png_file.name
                    image.save(backup_path)
                
                # Resize image while preserving transparency
                resized_image = image.resize((new_width, new_height), resample_filter)
                
                # Save resized image, overwriting original
                resized_image.save(png_file, 'PNG', optimize=True)
                
                processed_count += 1
                logger.debug(f"Resized {png_file.name}: {original_size} -> {new_width}x{new_height}")
                
        except Exception as e:
            failed_count += 1
            logger.error(f"Failed to resize {png_file.name}: {e}")
            continue
    
    logger.info(f"Resize operation completed: {processed_count} files processed, {failed_count} failed")
    
    if backup and processed_count > 0:
        logger.info(f"Original files backed up to: {backup_folder}")
    
    if failed_count > 0:
        logger.warning(f"{failed_count} files failed to resize")


def batch_resize_png_sequences(
    base_folder: str,
    new_width: int,
    new_height: int,
    backup: bool = True,
    quality: str = 'bilinear'
) -> None:
    """
    Batch resize multiple PNG sequence folders
    
    Args:
        base_folder: Base folder containing multiple PNG sequence folders
        new_width: New width in pixels
        new_height: New height in pixels
        backup: Whether to create backup of original files
        quality: Resize quality
    """
    base_path = Path(base_folder)
    
    if not base_path.exists() or not base_path.is_dir():
        raise IOError(f"Base folder does not exist or is not a directory: {base_folder}")
    
    # Find all subdirectories that contain PNG sequence files
    sequence_folders = []
    for subfolder in base_path.iterdir():
        if subfolder.is_dir():
            # Check if this folder contains PNG sequence files
            png_files = list(subfolder.glob("frame_*.png"))
            if png_files:
                sequence_folders.append(subfolder)
    
    if not sequence_folders:
        logger.warning(f"No PNG sequence folders found in: {base_folder}")
        return
    
    logger.info(f"Found {len(sequence_folders)} PNG sequence folders to resize")
    
    success_count = 0
    failed_folders = []
    
    for folder in sequence_folders:
        try:
            logger.info(f"Processing folder: {folder.name}")
            resize_png_sequence_files(str(folder), new_width, new_height, backup, quality)
            success_count += 1
        except Exception as e:
            logger.error(f"Failed to process folder {folder.name}: {e}")
            failed_folders.append(folder.name)
    
    logger.info(f"Batch resize completed: {success_count}/{len(sequence_folders)} folders processed successfully")
    
    if failed_folders:
        logger.warning(f"Failed folders: {', '.join(failed_folders)}")


def get_png_sequence_info(png_sequence_folder: str) -> dict:
    """
    Get information about a PNG sequence folder
    
    Args:
        png_sequence_folder: Path to PNG sequence folder
        
    Returns:
        Dictionary with sequence information
    """
    folder_path = Path(png_sequence_folder)
    
    if not folder_path.exists() or not folder_path.is_dir():
        raise IOError(f"PNG sequence folder does not exist or is not a directory: {png_sequence_folder}")
    
    # Find PNG files
    png_files = list(folder_path.glob("frame_*.png"))
    
    if not png_files:
        return {
            'folder': str(folder_path),
            'frame_count': 0,
            'width': None,
            'height': None,
            'total_size_mb': 0
        }
    
    png_files.sort(key=lambda x: x.name)
    
    # Get dimensions from first frame
    first_frame = Image.open(png_files[0])
    width, height = first_frame.size
    first_frame.close()
    
    # Calculate total size
    total_size = sum(f.stat().st_size for f in png_files)
    
    return {
        'folder': str(folder_path),
        'frame_count': len(png_files),
        'width': width,
        'height': height,
        'total_size_mb': total_size / (1024 * 1024)
    }


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(
        description="PNG Sequence Resizer - Resize PNG sequence files and overwrite originals"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Resize single folder command
    resize_parser = subparsers.add_parser('resize', help='Resize PNG sequence files in a folder')
    resize_parser.add_argument("folder", help="PNG sequence folder path")
    resize_parser.add_argument("width", type=int, help="New width in pixels")
    resize_parser.add_argument("height", type=int, help="New height in pixels")
    resize_parser.add_argument("--no-backup", action='store_true', 
                              help="Don't create backup of original files")
    resize_parser.add_argument("--quality", choices=['nearest', 'bilinear', 'bicubic', 'lanczos'],
                              default='bilinear', help="Resize quality (default: bilinear)")
    
    # Batch resize command
    batch_parser = subparsers.add_parser('batch', help='Batch resize multiple PNG sequence folders')
    batch_parser.add_argument("base_folder", help="Base folder containing PNG sequence folders")
    batch_parser.add_argument("width", type=int, help="New width in pixels")
    batch_parser.add_argument("height", type=int, help="New height in pixels")
    batch_parser.add_argument("--no-backup", action='store_true', 
                             help="Don't create backup of original files")
    batch_parser.add_argument("--quality", choices=['nearest', 'bilinear', 'bicubic', 'lanczos'],
                             default='bilinear', help="Resize quality (default: bilinear)")
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Get information about PNG sequence folder')
    info_parser.add_argument("folder", help="PNG sequence folder path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == 'resize':
            resize_png_sequence_files(
                args.folder,
                args.width,
                args.height,
                backup=not args.no_backup,
                quality=args.quality
            )
            
        elif args.command == 'batch':
            batch_resize_png_sequences(
                args.base_folder,
                args.width,
                args.height,
                backup=not args.no_backup,
                quality=args.quality
            )
            
        elif args.command == 'info':
            info = get_png_sequence_info(args.folder)
            print(f"PNG Sequence Information:")
            print(f"  Folder: {info['folder']}")
            print(f"  Frame count: {info['frame_count']}")
            if info['frame_count'] > 0:
                print(f"  Dimensions: {info['width']}x{info['height']}")
                print(f"  Total size: {info['total_size_mb']:.2f} MB")
            else:
                print("  No PNG sequence files found")
        
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
