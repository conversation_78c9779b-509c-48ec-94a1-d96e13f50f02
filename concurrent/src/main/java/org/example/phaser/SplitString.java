package org.example.phaser;

import java.util.ArrayList;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/13
 **/
public class SplitString {

    private static final Integer[] rewards = {1, 2, 5, 10};

    public static void get(long totalReward, ArrayList<Integer> result) {

// 当 totalReward = 0 时，证明它是满足条件的解，结束嵌套调用，输出解
        if (totalReward == 0) {
            System.out.println(result);
            return;
        }
// 当 totalReward < 0 时，证明它不是满足条件的解，不输出
        else if (totalReward < 0) {
            return;
        } else {
            for (int i = 0; i < rewards.length; i++) {
                ArrayList<Integer> newResult = (ArrayList<Integer>) (result.clone());
                newResult.add(rewards[i]);
                get(totalReward - rewards[i], newResult);
            }
        }
    }


    public static void main(String[] args) {

        get(10, new ArrayList<>());


        // int i = -4;
        //
        // System.out.println(i >>> 1);
        // String video = "http://qpark-dev.oss-cn-zhangjiakou.aliyuncs.com/qpark/2022/6/7/d2b00b9eeddf4f23ac053bb3be70dc66/4fc413c1-1cfc-4f2a-b234-c2e936d52966.mp4";
        //
        // System.out.println("http://qpark-dev.oss-cn-zhangjiakou.aliyuncs.com".split("://")[1]);
        // System.out.println(video.split("qpark-dev.oss-cn-zhangjiakou.aliyuncs.com/")[1]);


        // Pattern p = Pattern.compile("://(.*?)/(.*)", Pattern.CASE_INSENSITIVE);
        // // Matcher m = p.matcher("http://qpark-dev.oss-cn-zhangjiakou.aliyuncs.com/qpark/2022/6/7/d2b00b9eeddf4f23ac053bb3be70dc66/4fc413c1-1cfc-4f2a-b234-c2e936d52966.mp4");
        // // Matcher m = p.matcher("/qpark/2022/6/7/d2b00b9eeddf4f23ac053bb3be70dc66/4fc413c1-1cfc-4f2a-b234-c2e936d52966.mp4");
        // Matcher m = p.matcher("http://qpark-dev.oss-cn-zhangjiakou.aliyuncs.com");
        // if (m.find() && null != m.group(2)) {
        //     System.out.println("/" + m.group(2));

    }
}
