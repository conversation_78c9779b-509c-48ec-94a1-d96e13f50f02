package org.example.phaser;

import java.util.concurrent.Phaser;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/6
 **/
public class PhaserAdvance {

    public static void main(String[] args) {
        int repeats = 3;

        Phaser phaser = new Phaser() {
            @Override
            protected boolean onAdvance(int phase, int registeredParties) {
                System.out.println("----------------PHASE[" + phase + "],Parties[" + registeredParties + "]");
                return phase + 1 >= repeats || registeredParties == 0;
            }
        };
        for (int i = 0; i < 10; ++i) {
            phaser.register();
            new Thread(() -> {
                while (!phaser.isTerminated()) {   //只要Phaser没有终止, 各个线程的任务就会一直执行
                    phaser.arriveAndAwaitAdvance();     // 等待其它参与者线程到达
                    System.out.println(Thread.currentThread().getName() + ": 执行完任务");
                }
            }, "Thread-" + i).start();
        }
    }
}
