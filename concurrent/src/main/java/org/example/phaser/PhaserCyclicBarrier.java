package org.example.phaser;

import java.util.concurrent.Phaser;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/6
 **/
public class PhaserCyclicBarrier {

    public static void main(String[] args) {
        Phaser phaser = new Phaser(3);

        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + ":" + phaser.arriveAndAwaitAdvance());
        }, "Thread_1").start();
        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + ":" + phaser.arriveAndAwaitAdvance());
        }, "Thread_2").start();
        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + ":" + phaser.arriveAndAwaitAdvance());
        }, "Thread_3").start();

    }
}
