package org.example.exchanger;

import java.util.concurrent.Exchanger;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/3
 **/
public class ExchangerBasicSample {

    public static void main(String[] args) {
        Exchanger<String> exchanger = new Exchanger<>();

        new Thread(() -> {
            try {
                System.out.println("A" + exchanger.exchange("A"));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
        new Thread(() -> {
            try {
                System.out.println("B" + exchanger.exchange("B"));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
        new Thread(() -> {
            try {
                System.out.println("C" + exchanger.exchange("C"));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
    }


}
