package org.example.copyOnWrite;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/1/6
 **/
public class CopyOnWrite {

    public static void main(String[] args) throws InterruptedException {

        List<Integer> list = new CopyOnWriteArrayList<>();

        list.add(1);
        list.add(2);
        list.add(3);


        new Thread(() -> {

        }, "T1").start();

        new Thread(() -> {

        }, "T2").start();

        Thread.sleep(50_000);
    }
}
