package org.example.recursive;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/24
 **/
public class Solution {

    public static void main(String[] args) {
        ListNode head = new ListNode(1, new ListNode(2, new ListNode(3, new ListNode(4, null))));
        removeElements(head, 3);

    }

    public static ListNode removeElements(ListNode head, int val) {
        if (null == head) {
            return head;
        }
        head.next = removeElements(head.next, val);
        return val == head.val ? head.next : head;
    }

    public static class ListNode {
        int val;
        ListNode next;
        ListNode() {}
        ListNode(int val) {
            this.val = val;
        }
        ListNode(int val, ListNode next) {
            this.val = val;
            this.next = next;
        }
    }
}
