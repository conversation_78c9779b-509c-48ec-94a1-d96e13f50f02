package org.example.linkHashMap_keyset;

import java.io.File;
import java.io.IOException;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * <p>文件名称:org.example.linkHashMap_keyset.FileList</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/7/14
 */
public class FileList {

    public static void main(String[] args) throws IOException {

        // File[] files = new File("/Users/<USER>/IdeaProjects/qpark.location.srv/location-admin-server/target/classes/com/qudian/qpark/config/").listFiles();
        // Arrays.sort(files, Comparator.comparing(File::getName));
        //
        // Arrays.stream(files).forEach(f -> System.out.println(f.getName()));

        // System.out.println(new File("/Users/<USER>/IdeaProjects/qpark.location.srv").listFiles());

        // JarFile jarFile = new JarFile(new File("/Users/<USER>/.m2/repository/com/qudian/qpark/location-admin-server/1.0-SNAPSHOT/location-admin-server-1.0-SNAPSHOT.jar"));
        JarFile jarFile = new JarFile(new File("/Users/<USER>/Desktop/location-admin-server.jar"));
        Enumeration<JarEntry> entries = jarFile.entries();
        while (entries.hasMoreElements()) {
            System.out.println(entries.nextElement().getName());
        }

    }
}
