#!/usr/bin/env python3
"""
PNG Sequence to Binary Converter
Converts PNG sequence files to binary format with transparency support.
Based on the Java implementation in ImagePngSe2binUtil.java
"""

import os
import struct
import zlib
import logging
from pathlib import Path
from typing import List, Optional, Tuple
from enum import Enum
from PIL import Image
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ColorMode(Enum):
    """Color mode enumeration matching Java implementation"""
    RGB565 = 1      # 2 bytes, RGB565, no alpha channel (for CG images)
    RGB565A8 = 2    # 3 bytes, RGB565 + A8, with alpha channel (for character images with transparent background)
    ARGB8888 = 3    # 4 bytes, RGBA, with alpha channel


def convert_to_rgb565(image: Image.Image) -> bytes:
    """
    Convert PIL image to RGB565 format (2 bytes per pixel, no alpha)
    Matches Java RGB565 implementation
    """
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    width, height = image.size
    data = bytearray()
    
    for y in range(height):
        for x in range(width):
            r, g, b = image.getpixel((x, y))
            
            # Convert to RGB565 format (little endian)
            pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3)
            data.extend(struct.pack('<H', pixel))
    
    return bytes(data)


def convert_to_rgb565a8(image: Image.Image) -> bytes:
    """
    Convert PIL image to RGB565A8 format (3 bytes per pixel: RGB565 + Alpha)
    Matches Java RGB565A8 implementation for transparent backgrounds
    """
    if image.mode != 'RGBA':
        image = image.convert('RGBA')
    
    width, height = image.size
    data = bytearray()
    
    for y in range(height):
        for x in range(width):
            r, g, b, a = image.getpixel((x, y))
            
            # Convert RGB to RGB565 format (little endian)
            pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3)
            data.extend(struct.pack('<H', pixel))
            # Add alpha channel
            data.extend(struct.pack('<B', a))
    
    return bytes(data)


def convert_to_argb8888(image: Image.Image) -> bytes:
    """
    Convert PIL image to ARGB8888 format (4 bytes per pixel: BGRA order, little endian)
    Matches Java ARGB8888 implementation
    """
    if image.mode != 'RGBA':
        image = image.convert('RGBA')
    
    width, height = image.size
    data = bytearray()
    
    for y in range(height):
        for x in range(width):
            r, g, b, a = image.getpixel((x, y))
            
            # BGRA order (little endian) to match Java implementation
            data.extend([b, g, r, a])
    
    return bytes(data)


def compress_data_with_zlib(data: bytes) -> bytes:
    """
    Compress data using zlib with compression level 6
    Matches Java zlib compression implementation
    """
    compressor = zlib.compressobj(level=6)
    compressed = compressor.compress(data)
    compressed += compressor.flush()
    return compressed


def read_png_sequence_frames(png_sequence_folder: str) -> List[Image.Image]:
    """
    Read PNG sequence frames from folder with naming pattern frame_xxxxxx.png
    Matches Java readPngSequenceFrames implementation
    """
    folder_path = Path(png_sequence_folder)
    
    if not folder_path.exists() or not folder_path.is_dir():
        raise IOError(f"PNG sequence folder does not exist or is not a directory: {png_sequence_folder}")
    
    # Find all PNG files matching frame_xxxxxx.png pattern
    png_files = []
    for file_path in folder_path.glob("frame_*.png"):
        if file_path.name.lower().endswith('.png'):
            png_files.append(file_path)
    
    if not png_files:
        raise IOError(f"No PNG files matching frame_xxxxxx.png pattern found in: {png_sequence_folder}")
    
    # Sort files by name to ensure correct order
    png_files.sort(key=lambda x: x.name)
    
    frames = []
    for png_file in png_files:
        try:
            image = Image.open(png_file)
            if image is None:
                logger.warning(f"Skipping unreadable file: {png_file.name}")
                continue
            
            # Convert to RGBA to ensure consistency (matches Java TYPE_INT_ARGB)
            if image.mode != 'RGBA':
                rgba_image = Image.new('RGBA', image.size, (0, 0, 0, 0))
                if image.mode == 'P' and 'transparency' in image.info:
                    # Handle palette mode with transparency
                    image = image.convert('RGBA')
                else:
                    rgba_image.paste(image, (0, 0))
                    image = rgba_image
            
            frames.append(image.copy())
            
        except Exception as e:
            logger.error(f"Failed to read PNG file: {png_file.name} - {e}")
            continue
    
    if not frames:
        raise IOError(f"Unable to read any valid frames from PNG sequence folder: {png_sequence_folder}")
    
    logger.info(f"Successfully read {len(frames)} frames")
    return frames


def resize_frames(frames: List[Image.Image], width: int, height: int) -> List[Image.Image]:
    """
    Resize all frames to specified dimensions with bilinear interpolation
    Matches Java frame resizing implementation
    """
    resized_frames = []
    for frame in frames:
        resized = frame.resize((width, height), Image.Resampling.BILINEAR)
        resized_frames.append(resized)
    return resized_frames


def downsample_frames(frames: List[Image.Image], max_frames: int) -> List[Image.Image]:
    """
    Downsample frames if count exceeds max_frames
    Matches Java frame downsampling implementation
    """
    if len(frames) <= max_frames:
        return frames

    # Calculate skip factor to get approximately max_frames
    skip_factor = len(frames) // max_frames
    if skip_factor < 1:
        skip_factor = 1

    sampled_frames = frames[::skip_factor]

    # If we still have too many frames, take only the first max_frames
    if len(sampled_frames) > max_frames:
        sampled_frames = sampled_frames[:max_frames]

    return sampled_frames


def resize_png_sequence_files(
    png_sequence_folder: str,
    new_width: int,
    new_height: int,
    backup: bool = True,
    quality: str = 'bilinear'
) -> None:
    """
    Resize PNG sequence files and overwrite the original files

    Args:
        png_sequence_folder: Path to PNG sequence folder
        new_width: New width in pixels
        new_height: New height in pixels
        backup: Whether to create backup of original files (default: True)
        quality: Resize quality ('bilinear', 'bicubic', 'lanczos', 'nearest')

    Raises:
        IOError: If folder doesn't exist or no PNG files found
        ValueError: If invalid quality parameter
    """
    folder_path = Path(png_sequence_folder)

    if not folder_path.exists() or not folder_path.is_dir():
        raise IOError(f"PNG sequence folder does not exist or is not a directory: {png_sequence_folder}")

    # Find all PNG files matching frame_xxxxxx.png pattern
    png_files = []
    for file_path in folder_path.glob("frame_*.png"):
        if file_path.name.lower().endswith('.png'):
            png_files.append(file_path)

    if not png_files:
        raise IOError(f"No PNG files matching frame_xxxxxx.png pattern found in: {png_sequence_folder}")

    # Sort files by name to ensure correct order
    png_files.sort(key=lambda x: x.name)

    # Map quality string to PIL resampling filter
    quality_map = {
        'nearest': Image.Resampling.NEAREST,
        'bilinear': Image.Resampling.BILINEAR,
        'bicubic': Image.Resampling.BICUBIC,
        'lanczos': Image.Resampling.LANCZOS
    }

    if quality.lower() not in quality_map:
        raise ValueError(f"Invalid quality parameter: {quality}. Must be one of: {list(quality_map.keys())}")

    resample_filter = quality_map[quality.lower()]

    # Create backup folder if requested
    backup_folder = None
    if backup:
        backup_folder = folder_path / "backup_original"
        backup_folder.mkdir(exist_ok=True)
        logger.info(f"Created backup folder: {backup_folder}")

    processed_count = 0
    failed_count = 0

    logger.info(f"Starting to resize {len(png_files)} PNG files to {new_width}x{new_height}")

    for png_file in png_files:
        try:
            # Read original image
            with Image.open(png_file) as image:
                original_size = image.size

                # Create backup if requested
                if backup:
                    backup_path = backup_folder / png_file.name
                    image.save(backup_path)

                # Resize image while preserving transparency
                resized_image = image.resize((new_width, new_height), resample_filter)

                # Save resized image, overwriting original
                resized_image.save(png_file, 'PNG', optimize=True)

                processed_count += 1
                logger.debug(f"Resized {png_file.name}: {original_size} -> {new_width}x{new_height}")

        except Exception as e:
            failed_count += 1
            logger.error(f"Failed to resize {png_file.name}: {e}")
            continue

    logger.info(f"Resize operation completed: {processed_count} files processed, {failed_count} failed")

    if backup and processed_count > 0:
        logger.info(f"Original files backed up to: {backup_folder}")

    if failed_count > 0:
        logger.warning(f"{failed_count} files failed to resize")


def batch_resize_png_sequences(
    base_folder: str,
    new_width: int,
    new_height: int,
    backup: bool = True,
    quality: str = 'bilinear'
) -> None:
    """
    Batch resize multiple PNG sequence folders

    Args:
        base_folder: Base folder containing multiple PNG sequence folders
        new_width: New width in pixels
        new_height: New height in pixels
        backup: Whether to create backup of original files
        quality: Resize quality
    """
    base_path = Path(base_folder)

    if not base_path.exists() or not base_path.is_dir():
        raise IOError(f"Base folder does not exist or is not a directory: {base_folder}")

    # Find all subdirectories that contain PNG sequence files
    sequence_folders = []
    for subfolder in base_path.iterdir():
        if subfolder.is_dir():
            # Check if this folder contains PNG sequence files
            png_files = list(subfolder.glob("frame_*.png"))
            if png_files:
                sequence_folders.append(subfolder)

    if not sequence_folders:
        logger.warning(f"No PNG sequence folders found in: {base_folder}")
        return

    logger.info(f"Found {len(sequence_folders)} PNG sequence folders to resize")

    success_count = 0
    failed_folders = []

    for folder in sequence_folders:
        try:
            logger.info(f"Processing folder: {folder.name}")
            resize_png_sequence_files(str(folder), new_width, new_height, backup, quality)
            success_count += 1
        except Exception as e:
            logger.error(f"Failed to process folder {folder.name}: {e}")
            failed_folders.append(folder.name)

    logger.info(f"Batch resize completed: {success_count}/{len(sequence_folders)} folders processed successfully")

    if failed_folders:
        logger.warning(f"Failed folders: {', '.join(failed_folders)}")


def convert_png_sequence_to_bin_data(
    png_sequence_folder: str,
    width: Optional[int] = None,
    height: Optional[int] = None,
    max_frames: Optional[int] = None,
    color_mode: ColorMode = ColorMode.RGB565A8,
    enable_compression: bool = True
) -> bytes:
    """
    Convert PNG sequence folder to binary data
    Matches Java convertPngSequence2BinData implementation
    """
    # Read frames
    frames = read_png_sequence_frames(png_sequence_folder)
    
    # Determine dimensions
    if width is None or height is None:
        first_frame = frames[0]
        width = width or first_frame.width
        height = height or first_frame.height
    else:
        # Resize frames if dimensions specified
        frames = resize_frames(frames, width, height)
    
    # Downsample frames if needed
    if max_frames is not None and len(frames) > max_frames:
        frames = downsample_frames(frames, max_frames)
    
    return process_frames_to_bin_data(frames, width, height, color_mode, enable_compression, 
                                    Path(png_sequence_folder).name)


def process_frames_to_bin_data(
    frames: List[Image.Image],
    width: int,
    height: int,
    color_mode: ColorMode,
    enable_compression: bool,
    source_name: str
) -> bytes:
    """
    Process frames to binary data with custom header format
    Matches Java processFramesToBinData implementation
    """
    # Color mode converters
    converters = {
        ColorMode.RGB565: convert_to_rgb565,
        ColorMode.RGB565A8: convert_to_rgb565a8,
        ColorMode.ARGB8888: convert_to_argb8888
    }
    
    converter = converters[color_mode]
    
    # Prepare binary data
    header_data = bytearray()
    frame_data_list = []
    total_original_size = 0
    total_compressed_size = 0
    
    # Write header (little endian format matching Java implementation)
    # Total frames (4 bytes)
    header_data.extend(struct.pack('<I', len(frames)))
    # Width (4 bytes)
    header_data.extend(struct.pack('<I', width))
    # Height (4 bytes)
    header_data.extend(struct.pack('<I', height))
    # Color mode (1 byte) - ordinal + 1 to match Java
    header_data.extend(struct.pack('<B', color_mode.value))
    
    # Calculate data offset: header (13 bytes) + frame index entries (8 bytes per frame)
    data_offset = 13 + len(frames) * 8
    
    # Process each frame
    for frame in frames:
        original_frame_data = converter(frame)
        total_original_size += len(original_frame_data)
        
        if enable_compression:
            frame_data = compress_data_with_zlib(original_frame_data)
        else:
            frame_data = original_frame_data
        
        total_compressed_size += len(frame_data)
        frame_data_list.append(frame_data)
        
        # Write frame index entry (offset and size, both 4 bytes little endian)
        header_data.extend(struct.pack('<I', data_offset))
        header_data.extend(struct.pack('<I', len(frame_data)))
        data_offset += len(frame_data)
    
    # Combine header and frame data
    result = header_data
    for frame_data in frame_data_list:
        result.extend(frame_data)
    
    # Log compression statistics
    if enable_compression:
        compression_ratio = total_compressed_size / total_original_size if total_original_size > 0 else 1.0
        logger.info(f"Generated binary data for {source_name}: "
                   f"Size: {len(result) / 1024:.2f} KB, "
                   f"Frames: {len(frames)}, "
                   f"Compression: {compression_ratio * 100:.2f}% "
                   f"(Original: {total_original_size / 1024:.2f} KB -> "
                   f"Compressed: {total_compressed_size / 1024:.2f} KB)")
    else:
        logger.info(f"Generated binary data for {source_name}: "
                   f"Size: {len(result) / 1024:.2f} KB, "
                   f"Frames: {len(frames)} (uncompressed)")
    
    return bytes(result)


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(
        description="PNG Sequence Processing Tool - Convert to binary or resize PNG sequences"
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Convert command
    convert_parser = subparsers.add_parser('convert', help='Convert PNG sequence to binary format')
    convert_parser.add_argument("input_folder", help="Input PNG sequence folder path")
    convert_parser.add_argument("-o", "--output", help="Output binary file path (default: input_folder.bin)")
    convert_parser.add_argument("--width", type=int, help="Output width in pixels")
    convert_parser.add_argument("--height", type=int, help="Output height in pixels")
    convert_parser.add_argument("--max-frames", type=int, help="Maximum number of frames")
    convert_parser.add_argument("--color-mode", choices=['RGB565', 'RGB565A8', 'ARGB8888'],
                               default='RGB565A8', help="Color mode (default: RGB565A8)")
    convert_parser.add_argument("--no-compression", action='store_true',
                               help="Disable zlib compression")

    # Resize command
    resize_parser = subparsers.add_parser('resize', help='Resize PNG sequence files in place')
    resize_parser.add_argument("input_folder", help="Input PNG sequence folder path")
    resize_parser.add_argument("width", type=int, help="New width in pixels")
    resize_parser.add_argument("height", type=int, help="New height in pixels")
    resize_parser.add_argument("--no-backup", action='store_true',
                              help="Don't create backup of original files")
    resize_parser.add_argument("--quality", choices=['nearest', 'bilinear', 'bicubic', 'lanczos'],
                              default='bilinear', help="Resize quality (default: bilinear)")

    # Batch resize command
    batch_resize_parser = subparsers.add_parser('batch-resize', help='Batch resize multiple PNG sequence folders')
    batch_resize_parser.add_argument("base_folder", help="Base folder containing PNG sequence folders")
    batch_resize_parser.add_argument("width", type=int, help="New width in pixels")
    batch_resize_parser.add_argument("height", type=int, help="New height in pixels")
    batch_resize_parser.add_argument("--no-backup", action='store_true',
                                    help="Don't create backup of original files")
    batch_resize_parser.add_argument("--quality", choices=['nearest', 'bilinear', 'bicubic', 'lanczos'],
                                    default='bilinear', help="Resize quality (default: bilinear)")

    args = parser.parse_args()

    # If no command specified, show help
    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == 'convert':
            # Determine output path
            if args.output:
                output_path = args.output
            else:
                output_path = Path(args.input_folder).name + ".bin"

            # Parse color mode
            color_mode = ColorMode[args.color_mode]

            # Convert PNG sequence to binary data
            bin_data = convert_png_sequence_to_bin_data(
                args.input_folder,
                width=args.width,
                height=args.height,
                max_frames=args.max_frames,
                color_mode=color_mode,
                enable_compression=not args.no_compression
            )

            # Write to file
            with open(output_path, 'wb') as f:
                f.write(bin_data)

            logger.info(f"Conversion completed: {output_path}")

        elif args.command == 'resize':
            # Resize PNG sequence files
            resize_png_sequence_files(
                args.input_folder,
                args.width,
                args.height,
                backup=not args.no_backup,
                quality=args.quality
            )

        elif args.command == 'batch-resize':
            # Batch resize PNG sequence folders
            batch_resize_png_sequences(
                args.base_folder,
                args.width,
                args.height,
                backup=not args.no_backup,
                quality=args.quality
            )

    except Exception as e:
        logger.error(f"Operation failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
