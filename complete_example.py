#!/usr/bin/env python3
"""
Complete Example: PNG Sequence Processing
Demonstrates both resize and conversion functionality
"""

import os
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw
from png_sequence_to_bin import convert_png_sequence_to_bin_data, ColorMode
from resize_png_sequence import resize_png_sequence_files, get_png_sequence_info


def create_sample_png_sequence(output_dir: str, num_frames: int = 10, width: int = 200, height: int = 200) -> str:
    """Create a sample PNG sequence with transparency for demonstration"""
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"Creating {num_frames} sample PNG frames ({width}x{height}) with transparency...")
    
    for i in range(num_frames):
        # Create RGBA image with transparent background
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw animated background gradient
        for y in range(height):
            alpha = int(100 + 100 * (y / height))
            color = (50, 50 + i * 20, 200 - i * 15, alpha)
            draw.line([(0, y), (width, y)], fill=color)
        
        # Draw moving circle
        circle_x = int((width - 40) * (i / (num_frames - 1)))
        circle_y = height // 2
        circle_color = (255, 255 - i * 20, i * 25, 200)
        draw.ellipse([circle_x, circle_y - 20, circle_x + 40, circle_y + 20], fill=circle_color)
        
        # Draw frame counter with semi-transparent background
        text_bg_color = (0, 0, 0, 150)
        draw.rectangle([5, 5, 80, 25], fill=text_bg_color)
        draw.text((10, 10), f"Frame {i:02d}", fill=(255, 255, 255, 255))
        
        # Add some decorative elements
        for j in range(5):
            star_x = 20 + j * 30
            star_y = 30 + int(10 * (i / num_frames))
            star_color = (255, 255, 0, 180)
            draw.polygon([
                (star_x, star_y - 5),
                (star_x + 3, star_y + 3),
                (star_x - 3, star_y + 3)
            ], fill=star_color)
        
        # Save with proper naming convention
        filename = f"frame_{i:06d}.png"
        img.save(os.path.join(output_dir, filename))
    
    print(f"Created sample PNG sequence in: {output_dir}")
    return output_dir


def demonstrate_complete_workflow():
    """Demonstrate complete PNG sequence processing workflow"""
    print("=" * 60)
    print("PNG Sequence Processing - Complete Workflow Demonstration")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Step 1: Create sample PNG sequence
        print("\n1. Creating sample PNG sequence...")
        original_dir = os.path.join(temp_dir, "original_sequence")
        create_sample_png_sequence(original_dir, 15, 200, 200)
        
        # Get original info
        original_info = get_png_sequence_info(original_dir)
        print(f"   Original: {original_info['width']}x{original_info['height']}, "
              f"{original_info['frame_count']} frames, "
              f"{original_info['total_size_mb']:.2f} MB")
        
        # Step 2: Convert original to binary (for comparison)
        print("\n2. Converting original sequence to binary...")
        original_bin_data = convert_png_sequence_to_bin_data(
            original_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        original_bin_path = os.path.join(temp_dir, "original.bin")
        with open(original_bin_path, 'wb') as f:
            f.write(original_bin_data)
        
        print(f"   Original binary size: {len(original_bin_data) / 1024:.2f} KB")
        
        # Step 3: Resize PNG sequence to different resolutions
        print("\n3. Resizing PNG sequence to different resolutions...")
        
        resolutions = [
            (128, 128, "medium"),
            (64, 64, "small"),
            (32, 32, "tiny")
        ]
        
        for width, height, size_name in resolutions:
            # Create copy for resizing
            resized_dir = os.path.join(temp_dir, f"resized_{size_name}")
            os.makedirs(resized_dir)
            
            # Copy original files
            import shutil
            for png_file in Path(original_dir).glob("frame_*.png"):
                shutil.copy2(png_file, resized_dir)
            
            # Resize
            print(f"   Resizing to {width}x{height} ({size_name})...")
            resize_png_sequence_files(resized_dir, width, height, backup=False, quality='lanczos')
            
            # Get resized info
            resized_info = get_png_sequence_info(resized_dir)
            print(f"     Resized: {resized_info['width']}x{resized_info['height']}, "
                  f"{resized_info['frame_count']} frames, "
                  f"{resized_info['total_size_mb']:.2f} MB")
            
            # Convert resized to binary
            resized_bin_data = convert_png_sequence_to_bin_data(
                resized_dir,
                color_mode=ColorMode.RGB565A8,
                enable_compression=True
            )
            
            resized_bin_path = os.path.join(temp_dir, f"resized_{size_name}.bin")
            with open(resized_bin_path, 'wb') as f:
                f.write(resized_bin_data)
            
            print(f"     Binary size: {len(resized_bin_data) / 1024:.2f} KB "
                  f"({len(resized_bin_data) / len(original_bin_data) * 100:.1f}% of original)")
        
        # Step 4: Compare different color modes
        print("\n4. Comparing different color modes (using 64x64 version)...")
        
        small_dir = os.path.join(temp_dir, "resized_small")
        color_modes = [
            (ColorMode.RGB565, "RGB565 (no transparency)"),
            (ColorMode.RGB565A8, "RGB565A8 (with transparency)"),
            (ColorMode.ARGB8888, "ARGB8888 (full color + transparency)")
        ]
        
        for color_mode, description in color_modes:
            bin_data = convert_png_sequence_to_bin_data(
                small_dir,
                color_mode=color_mode,
                enable_compression=True
            )
            
            print(f"   {description}: {len(bin_data) / 1024:.2f} KB")
        
        # Step 5: Compare compression settings
        print("\n5. Comparing compression settings (using 64x64 RGB565A8)...")
        
        # With compression
        compressed_data = convert_png_sequence_to_bin_data(
            small_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        # Without compression
        uncompressed_data = convert_png_sequence_to_bin_data(
            small_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=False
        )
        
        compression_ratio = len(compressed_data) / len(uncompressed_data)
        
        print(f"   Compressed: {len(compressed_data) / 1024:.2f} KB")
        print(f"   Uncompressed: {len(uncompressed_data) / 1024:.2f} KB")
        print(f"   Compression ratio: {compression_ratio * 100:.1f}%")
        
        # Step 6: Frame limiting demonstration
        print("\n6. Demonstrating frame limiting...")
        
        frame_limits = [10, 5, 3]
        for max_frames in frame_limits:
            limited_data = convert_png_sequence_to_bin_data(
                original_dir,
                max_frames=max_frames,
                color_mode=ColorMode.RGB565A8,
                enable_compression=True
            )
            
            print(f"   Max {max_frames} frames: {len(limited_data) / 1024:.2f} KB")
        
        # Step 7: Summary
        print("\n7. Summary and Recommendations:")
        print("   - Use RGB565A8 for images with transparency")
        print("   - Use RGB565 for images without transparency (smaller size)")
        print("   - Use ARGB8888 for highest quality (largest size)")
        print("   - Enable compression for significantly smaller files")
        print("   - Resize images to target resolution before conversion")
        print("   - Limit frames if animation length is not critical")
        
        print(f"\n   All output files are in: {temp_dir}")
        print("   (Note: Temporary directory will be cleaned up automatically)")


def demonstrate_batch_processing():
    """Demonstrate batch processing of multiple PNG sequences"""
    print("\n" + "=" * 60)
    print("Batch Processing Demonstration")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create multiple PNG sequence folders
        base_dir = os.path.join(temp_dir, "sequences")
        os.makedirs(base_dir)
        
        sequences = [
            ("character_walk", 8, 150, 150),
            ("ui_animation", 12, 100, 100),
            ("background_effect", 20, 300, 200)
        ]
        
        print("\nCreating multiple PNG sequences...")
        for name, frames, width, height in sequences:
            seq_dir = os.path.join(base_dir, name)
            create_sample_png_sequence(seq_dir, frames, width, height)
            
            info = get_png_sequence_info(seq_dir)
            print(f"   {name}: {info['width']}x{info['height']}, "
                  f"{info['frame_count']} frames, {info['total_size_mb']:.2f} MB")
        
        print("\nBatch resizing all sequences to 64x64...")
        from resize_png_sequence import batch_resize_png_sequences
        batch_resize_png_sequences(base_dir, 64, 64, backup=True, quality='lanczos')
        
        print("\nAfter batch resize:")
        total_original_size = 0
        total_resized_size = 0
        
        for name, _, _, _ in sequences:
            seq_dir = os.path.join(base_dir, name)
            info = get_png_sequence_info(seq_dir)
            
            # Get backup size
            backup_dir = Path(seq_dir) / "backup_original"
            backup_info = get_png_sequence_info(str(backup_dir))
            
            total_original_size += backup_info['total_size_mb']
            total_resized_size += info['total_size_mb']
            
            print(f"   {name}: {info['width']}x{info['height']}, "
                  f"{info['frame_count']} frames, {info['total_size_mb']:.2f} MB "
                  f"(was {backup_info['total_size_mb']:.2f} MB)")
        
        size_reduction = (1 - total_resized_size / total_original_size) * 100
        print(f"\nTotal size reduction: {size_reduction:.1f}% "
              f"({total_original_size:.2f} MB -> {total_resized_size:.2f} MB)")


def main():
    """Run complete demonstration"""
    try:
        demonstrate_complete_workflow()
        demonstrate_batch_processing()
        
        print("\n" + "=" * 60)
        print("Demonstration completed successfully!")
        print("=" * 60)
        
        print("\nCommand-line usage examples:")
        print("1. Convert PNG sequence to binary:")
        print("   python png_sequence_to_bin.py convert path/to/png_folder")
        print("2. Resize PNG sequence:")
        print("   python resize_png_sequence.py resize path/to/png_folder 320 240")
        print("3. Batch resize multiple folders:")
        print("   python resize_png_sequence.py batch path/to/base_folder 320 240")
        
    except Exception as e:
        print(f"Demonstration failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
