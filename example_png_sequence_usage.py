#!/usr/bin/env python3
"""
Example usage of PNG Sequence to Binary Converter
Demonstrates various ways to use the png_sequence_to_bin module
"""

import os
from pathlib import Path
from png_sequence_to_bin import (
    convert_png_sequence_to_bin_data,
    ColorMode,
    logger
)


def example_basic_conversion():
    """Example 1: Basic PNG sequence conversion with transparency"""
    print("=== Example 1: Basic PNG sequence conversion ===")
    
    # Assuming you have a folder with PNG sequence files like:
    # frame_000000.png, frame_000001.png, frame_000002.png, etc.
    png_folder = "path/to/your/png_sequence_folder"
    
    if not os.path.exists(png_folder):
        print(f"PNG folder not found: {png_folder}")
        print("Please create a folder with PNG sequence files named frame_xxxxxx.png")
        return
    
    try:
        # Convert with default settings (RGB565A8 mode with compression)
        bin_data = convert_png_sequence_to_bin_data(
            png_folder,
            color_mode=ColorMode.RGB565A8,  # Preserves transparency
            enable_compression=True
        )
        
        # Save to file
        output_path = "output_basic.bin"
        with open(output_path, 'wb') as f:
            f.write(bin_data)
        
        print(f"Basic conversion completed: {output_path}")
        print(f"Output size: {len(bin_data) / 1024:.2f} KB")
        
    except Exception as e:
        print(f"Basic conversion failed: {e}")


def example_custom_dimensions():
    """Example 2: Convert with custom dimensions"""
    print("\n=== Example 2: Convert with custom dimensions ===")
    
    png_folder = "path/to/your/png_sequence_folder"
    
    if not os.path.exists(png_folder):
        print(f"PNG folder not found: {png_folder}")
        return
    
    try:
        # Convert with custom dimensions (will resize all frames)
        bin_data = convert_png_sequence_to_bin_data(
            png_folder,
            width=320,
            height=240,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        output_path = "output_resized.bin"
        with open(output_path, 'wb') as f:
            f.write(bin_data)
        
        print(f"Resized conversion completed: {output_path}")
        print(f"Output size: {len(bin_data) / 1024:.2f} KB")
        
    except Exception as e:
        print(f"Resized conversion failed: {e}")


def example_frame_limiting():
    """Example 3: Convert with frame count limiting"""
    print("\n=== Example 3: Convert with frame count limiting ===")
    
    png_folder = "path/to/your/png_sequence_folder"
    
    if not os.path.exists(png_folder):
        print(f"PNG folder not found: {png_folder}")
        return
    
    try:
        # Convert with maximum 30 frames (will downsample if more frames exist)
        bin_data = convert_png_sequence_to_bin_data(
            png_folder,
            max_frames=30,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        output_path = "output_limited_frames.bin"
        with open(output_path, 'wb') as f:
            f.write(bin_data)
        
        print(f"Frame-limited conversion completed: {output_path}")
        print(f"Output size: {len(bin_data) / 1024:.2f} KB")
        
    except Exception as e:
        print(f"Frame-limited conversion failed: {e}")


def example_different_color_modes():
    """Example 4: Compare different color modes"""
    print("\n=== Example 4: Compare different color modes ===")
    
    png_folder = "path/to/your/png_sequence_folder"
    
    if not os.path.exists(png_folder):
        print(f"PNG folder not found: {png_folder}")
        return
    
    color_modes = [
        (ColorMode.RGB565, "RGB565 (no transparency, 2 bytes/pixel)"),
        (ColorMode.RGB565A8, "RGB565A8 (with transparency, 3 bytes/pixel)"),
        (ColorMode.ARGB8888, "ARGB8888 (full color with transparency, 4 bytes/pixel)")
    ]
    
    for color_mode, description in color_modes:
        try:
            bin_data = convert_png_sequence_to_bin_data(
                png_folder,
                color_mode=color_mode,
                enable_compression=True
            )
            
            output_path = f"output_{color_mode.name.lower()}.bin"
            with open(output_path, 'wb') as f:
                f.write(bin_data)
            
            print(f"{description}: {output_path} ({len(bin_data) / 1024:.2f} KB)")
            
        except Exception as e:
            print(f"Failed to convert with {color_mode.name}: {e}")


def example_compression_comparison():
    """Example 5: Compare compressed vs uncompressed output"""
    print("\n=== Example 5: Compare compressed vs uncompressed ===")
    
    png_folder = "path/to/your/png_sequence_folder"
    
    if not os.path.exists(png_folder):
        print(f"PNG folder not found: {png_folder}")
        return
    
    try:
        # Compressed version
        compressed_data = convert_png_sequence_to_bin_data(
            png_folder,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        # Uncompressed version
        uncompressed_data = convert_png_sequence_to_bin_data(
            png_folder,
            color_mode=ColorMode.RGB565A8,
            enable_compression=False
        )
        
        # Save both versions
        with open("output_compressed.bin", 'wb') as f:
            f.write(compressed_data)
        
        with open("output_uncompressed.bin", 'wb') as f:
            f.write(uncompressed_data)
        
        compression_ratio = len(compressed_data) / len(uncompressed_data)
        
        print(f"Compressed: {len(compressed_data) / 1024:.2f} KB")
        print(f"Uncompressed: {len(uncompressed_data) / 1024:.2f} KB")
        print(f"Compression ratio: {compression_ratio * 100:.2f}%")
        
    except Exception as e:
        print(f"Compression comparison failed: {e}")


def example_batch_processing():
    """Example 6: Batch process multiple PNG sequence folders"""
    print("\n=== Example 6: Batch process multiple folders ===")
    
    # List of PNG sequence folders to process
    png_folders = [
        "path/to/sequence1",
        "path/to/sequence2",
        "path/to/sequence3"
    ]
    
    for i, png_folder in enumerate(png_folders):
        if not os.path.exists(png_folder):
            print(f"Skipping non-existent folder: {png_folder}")
            continue
        
        try:
            bin_data = convert_png_sequence_to_bin_data(
                png_folder,
                color_mode=ColorMode.RGB565A8,
                enable_compression=True
            )
            
            folder_name = Path(png_folder).name
            output_path = f"batch_output_{folder_name}.bin"
            
            with open(output_path, 'wb') as f:
                f.write(bin_data)
            
            print(f"Batch {i+1}: {folder_name} -> {output_path} ({len(bin_data) / 1024:.2f} KB)")
            
        except Exception as e:
            print(f"Batch processing failed for {png_folder}: {e}")


def create_test_png_sequence():
    """Helper function to create a test PNG sequence for demonstration"""
    print("\n=== Creating test PNG sequence ===")
    
    try:
        from PIL import Image, ImageDraw
        
        # Create test folder
        test_folder = "test_png_sequence"
        os.makedirs(test_folder, exist_ok=True)
        
        # Create 10 test frames with transparency
        for i in range(10):
            # Create RGBA image with transparency
            img = Image.new('RGBA', (100, 100), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw a colored circle that moves across the frame
            x = i * 10
            y = 50
            color = (255, i * 25, 255 - i * 25, 200)  # Semi-transparent color
            draw.ellipse([x, y, x + 20, y + 20], fill=color)
            
            # Save with proper naming convention
            filename = f"frame_{i:06d}.png"
            img.save(os.path.join(test_folder, filename))
        
        print(f"Created test PNG sequence in: {test_folder}")
        print("You can now run the examples with this test data")
        
        return test_folder
        
    except ImportError:
        print("PIL/Pillow not available for creating test sequence")
        return None


def main():
    """Run all examples"""
    print("PNG Sequence to Binary Converter - Examples")
    print("=" * 50)
    
    # Uncomment the line below to create a test PNG sequence
    # test_folder = create_test_png_sequence()
    
    # Run examples (update paths to your actual PNG sequence folders)
    example_basic_conversion()
    example_custom_dimensions()
    example_frame_limiting()
    example_different_color_modes()
    example_compression_comparison()
    example_batch_processing()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo use with your own data:")
    print("1. Create a folder with PNG files named frame_000000.png, frame_000001.png, etc.")
    print("2. Update the png_folder paths in the examples above")
    print("3. Run the examples or use the command line interface:")
    print("   python png_sequence_to_bin.py your_png_folder --color-mode RGB565A8")


if __name__ == "__main__":
    main()
