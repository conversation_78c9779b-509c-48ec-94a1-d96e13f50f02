#!/usr/bin/env python3
"""
ESP32S3特定的bin文件解析问题分析工具
"""

import struct
import sys
from pathlib import Path

def analyze_esp32_parsing_issue(file_path):
    """分析ESP32S3可能的解析问题"""
    
    # 目标错误值
    target_frame = 8
    target_width = 135  
    target_height = 240
    
    print(f"=== ESP32S3解析问题分析 ===")
    print(f"目标错误值: frame={target_frame}, width={target_width}, height={target_height}")
    print()
    
    with open(file_path, 'rb') as f:
        data = f.read(1024)  # 读取更多数据用于分析
    
    # 分析这些数值的二进制表示
    print("=== 目标值的二进制分析 ===")
    print(f"frame=8:   二进制={bin(target_frame)}, 十六进制=0x{target_frame:02X}")
    print(f"width=135: 二进制={bin(target_width)}, 十六进制=0x{target_width:02X}")
    print(f"height=240: 二进制={bin(target_height)}, 十六进制=0x{target_height:02X}")
    print()
    
    # 检查文件中是否存在这些值
    print("=== 在文件中搜索目标值 ===")
    found_positions = []
    
    for i, byte_val in enumerate(data):
        if byte_val == target_frame:
            print(f"找到 frame=8 在位置 {i} (0x{i:04X})")
            found_positions.append(('frame', i, target_frame))
        elif byte_val == target_width:
            print(f"找到 width=135 在位置 {i} (0x{i:04X})")
            found_positions.append(('width', i, target_width))
        elif byte_val == target_height:
            print(f"找到 height=240 在位置 {i} (0x{i:04X})")
            found_positions.append(('height', i, target_height))
    
    if not found_positions:
        print("在文件中未找到任何目标值")
    print()
    
    # 分析可能的数据损坏模式
    print("=== 可能的数据损坏分析 ===")
    
    # 检查是否是位移错误
    original_values = [41, 203, 360]  # 正确的值
    error_values = [8, 135, 240]     # 错误的值
    
    print("原始值 vs 错误值的关系分析:")
    for i, (orig, err) in enumerate(zip(original_values, error_values)):
        field_name = ['frame', 'width', 'height'][i]
        print(f"{field_name}: {orig} -> {err}")
        
        # 检查位操作关系
        xor_result = orig ^ err
        print(f"  XOR: {orig} ^ {err} = {xor_result} (0x{xor_result:02X})")
        
        # 检查位移关系
        for shift in range(1, 8):
            if (orig >> shift) == err:
                print(f"  右移 {shift} 位: {orig} >> {shift} = {err}")
            if (orig << shift) & 0xFF == err:
                print(f"  左移 {shift} 位: ({orig} << {shift}) & 0xFF = {err}")
        
        # 检查模运算关系
        for mod in [256, 128, 64, 32, 16]:
            if orig % mod == err:
                print(f"  模运算: {orig} % {mod} = {err}")
        
        print()
    
    # 检查是否是字节序转换错误
    print("=== 字节序转换错误分析 ===")
    
    # 将原始值按不同方式编码然后解码
    for field_name, orig_val in zip(['frame', 'width', 'height'], [41, 203, 360]):
        print(f"{field_name} ({orig_val}) 的各种编码解码:")
        
        # 4字节小端序编码
        encoded_le = struct.pack('<I', orig_val)
        print(f"  4字节小端序: {' '.join(f'{b:02X}' for b in encoded_le)}")
        
        # 尝试用不同方式解码
        for fmt, desc in [('B', '1字节'), ('H', '2字节小端'), ('>H', '2字节大端')]:
            try:
                if fmt == 'B':
                    decoded = encoded_le[0]  # 只取第一个字节
                else:
                    decoded = struct.unpack(fmt, encoded_le[:2])[0]
                print(f"    {desc}解码: {decoded}")
            except:
                pass
        print()
    
    # 检查内存对齐问题
    print("=== 内存对齐问题分析 ===")
    
    # 模拟不同的结构体对齐
    print("模拟C结构体的不同对齐方式:")
    
    # 正确的结构体 (packed)
    correct_data = struct.pack('<III B', 41, 203, 360, 2)
    print(f"正确结构体 (packed): {' '.join(f'{b:02X}' for b in correct_data)}")
    
    # 可能的错误对齐 (每个字段4字节对齐)
    aligned_data = struct.pack('<I I I I', 41, 203, 360, 2)
    print(f"4字节对齐结构体: {' '.join(f'{b:02X}' for b in aligned_data[:16])}")
    
    # 检查是否某种对齐方式能产生错误值
    for alignment in [1, 2, 4, 8]:
        print(f"\n{alignment}字节对齐分析:")
        try:
            # 模拟对齐后的读取
            offset = 0
            for field_name, expected_err in zip(['frame', 'width', 'height'], [8, 135, 240]):
                # 尝试从对齐位置读取
                if offset + 4 <= len(aligned_data):
                    value = struct.unpack('<I', aligned_data[offset:offset+4])[0]
                    if value == expected_err:
                        print(f"  {field_name}: 在偏移{offset}找到匹配值{expected_err}")
                offset += alignment
        except:
            pass
    
    print("\n=== 最可能的原因分析 ===")
    print("基于分析，最可能的原因是:")
    print("1. ESP32S3的解析代码使用了错误的数据类型或字节序")
    print("2. 文件在传输到ESP32S3时被损坏或截断")
    print("3. ESP32S3的内存对齐设置导致结构体读取偏移")
    print("4. ESP32S3使用了不同版本的文件格式定义")
    print()
    print("建议的调试步骤:")
    print("1. 在ESP32S3上打印文件的前16字节，确认是否为:")
    print("   29 00 00 00 CB 00 00 00 68 01 00 00 02")
    print("2. 检查ESP32S3的C代码是否正确使用了uint32_t和小端序")
    print("3. 尝试逐字节读取而不是结构体读取")
    print("4. 检查文件系统是否支持大文件")

def main():
    if len(sys.argv) != 2:
        print("用法: python esp32_debug_analysis.py <bin_file>")
        return
    
    analyze_esp32_parsing_issue(sys.argv[1])

if __name__ == "__main__":
    main()
