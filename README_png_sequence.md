# PNG Sequence Processing Tools

This Python toolkit provides comprehensive tools for processing PNG sequence files, including conversion to binary format and in-place resizing while preserving transparency information. It's designed to match the functionality of the Java implementation in `ImagePngSe2binUtil.java`.

## Features

### PNG Sequence to Binary Converter (`png_sequence_to_bin.py`)
- Processes PNG sequence files with naming pattern `frame_000000.png`, `frame_000001.png`, etc.
- Preserves transparency information in PNG files
- Supports multiple color modes:
  - `RGB565` (2 bytes/pixel, no transparency)
  - `RGB565A8` (3 bytes/pixel, with transparency)
  - `ARGB8888` (4 bytes/pixel, full color with transparency)
- Optional zlib compression for smaller output files
- Resizing and frame count limiting options
- Command-line interface for easy usage
- Matches the binary format of the Java implementation

### PNG Sequence Resizer (`resize_png_sequence.py`)
- Resize PNG sequence files in-place and overwrite original files
- Automatic backup creation (optional)
- Multiple resize quality options (nearest, bilinear, bicubic, lanczos)
- Batch processing of multiple PNG sequence folders
- Preserves transparency during resize operations
- Get information about PNG sequence folders

## Requirements

- Python 3.6+
- Pillow (PIL Fork) library for image processing
- zlib (included in Python standard library)

Install dependencies:

```bash
pip install pillow
```

## Usage

### PNG Sequence to Binary Converter

#### Command Line Interface

```bash
python png_sequence_to_bin.py convert input_folder [options]
```

Options:
- `-o, --output`: Output binary file path (default: input_folder.bin)
- `--width`: Output width in pixels
- `--height`: Output height in pixels
- `--max-frames`: Maximum number of frames
- `--color-mode`: Color mode (RGB565, RGB565A8, ARGB8888)
- `--no-compression`: Disable zlib compression

Examples:

```bash
# Basic conversion with transparency
python png_sequence_to_bin.py convert path/to/png_sequence

# Resize frames to 320x240
python png_sequence_to_bin.py convert path/to/png_sequence --width 320 --height 240

# Limit to 30 frames and use full color mode
python png_sequence_to_bin.py convert path/to/png_sequence --max-frames 30 --color-mode ARGB8888

# Disable compression
python png_sequence_to_bin.py convert path/to/png_sequence --no-compression
```

### PNG Sequence Resizer

#### Resize Single Folder

```bash
python resize_png_sequence.py resize folder_path width height [options]
```

Options:
- `--no-backup`: Don't create backup of original files
- `--quality`: Resize quality (nearest, bilinear, bicubic, lanczos)

Examples:

```bash
# Resize PNG sequence to 320x240 with backup
python resize_png_sequence.py resize path/to/png_sequence 320 240

# Resize without backup using bicubic quality
python resize_png_sequence.py resize path/to/png_sequence 320 240 --no-backup --quality bicubic
```

#### Batch Resize Multiple Folders

```bash
python resize_png_sequence.py batch base_folder width height [options]
```

Examples:

```bash
# Batch resize all PNG sequence folders to 320x240
python resize_png_sequence.py batch path/to/base_folder 320 240

# Batch resize without backup
python resize_png_sequence.py batch path/to/base_folder 320 240 --no-backup
```

#### Get Folder Information

```bash
python resize_png_sequence.py info folder_path
```

Example:

```bash
# Get information about PNG sequence folder
python resize_png_sequence.py info path/to/png_sequence
```

### As a Module

You can also use the tool as a Python module in your own code:

```python
from png_sequence_to_bin import convert_png_sequence_to_bin_data, ColorMode

# Convert PNG sequence to binary data
bin_data = convert_png_sequence_to_bin_data(
    "path/to/png_sequence",
    width=320,               # Optional: resize frames
    height=240,              # Optional: resize frames
    max_frames=30,           # Optional: limit frame count
    color_mode=ColorMode.RGB565A8,  # Use RGB565A8 for transparency
    enable_compression=True  # Enable zlib compression
)

# Save to file
with open("output.bin", "wb") as f:
    f.write(bin_data)
```

## Binary Format

The output binary file has the following format (little-endian):

1. **Header**:
   - Total frames (4 bytes)
   - Width (4 bytes)
   - Height (4 bytes)
   - Color mode (1 byte): 1=RGB565, 2=RGB565A8, 3=ARGB8888
   - Frame index entries (8 bytes per frame):
     - Frame data offset (4 bytes)
     - Frame data size (4 bytes)

2. **Frame Data**:
   - Compressed or uncompressed pixel data for each frame

## Color Modes

### RGB565 (2 bytes/pixel)
- No alpha channel
- 5 bits for red, 6 bits for green, 5 bits for blue
- Good for images without transparency

### RGB565A8 (3 bytes/pixel)
- RGB565 color format + 8-bit alpha channel
- Good for images with transparency
- Smaller file size than ARGB8888

### ARGB8888 (4 bytes/pixel)
- Full color with alpha channel
- 8 bits each for blue, green, red, and alpha (BGRA order)
- Highest quality but largest file size

## Example Code

See `example_png_sequence_usage.py` and `example_resize_usage.py` for detailed examples.

## Files Included

- `png_sequence_to_bin.py` - Main converter with both conversion and resize functionality
- `resize_png_sequence.py` - Dedicated PNG sequence resizer utility
- `example_png_sequence_usage.py` - Examples for conversion functionality
- `example_resize_usage.py` - Examples for resize functionality
- `complete_example.py` - Comprehensive demonstration of all features
- `test_png_sequence.py` - Tests for conversion functionality
- `test_resize_png_sequence.py` - Tests for resize functionality

## Key Features Summary

### Transparency Preservation
- Automatically detects and preserves PNG transparency
- Supports palette mode with transparency
- Maintains alpha channel information during resize operations
- Converts images to RGBA format for consistent processing

### Performance Optimizations
- Zlib compression reduces file sizes significantly (often 90%+ compression)
- Multiple resize quality options for speed vs quality trade-offs
- Batch processing for multiple folders
- Efficient memory usage with streaming operations

### Error Handling
- Comprehensive error checking and reporting
- Graceful handling of corrupted or invalid PNG files
- Automatic backup creation (optional) before resize operations
- Detailed logging for troubleshooting

## Notes

- The PNG files must follow the naming pattern `frame_000000.png`, `frame_000001.png`, etc.
- Files are sorted by name to determine the frame order
- The tool automatically converts images to the appropriate format with transparency
- When using compression, zlib level 6 is used to match the Java implementation
- The binary format is compatible with the Java implementation in `ImagePngSe2binUtil.java`
- Resize operations preserve transparency and use high-quality resampling algorithms
- Backup folders are created automatically unless disabled with `--no-backup`
