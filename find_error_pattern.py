#!/usr/bin/env python3
"""
寻找ESP32S3错误解析模式的精确工具
"""

import struct
import sys
from pathlib import Path

def find_error_pattern(file_path):
    """寻找能产生frame=8, width=135, height=240的确切位置和方式"""
    
    target_frame = 8
    target_width = 135
    target_height = 240
    
    print(f"=== 寻找错误模式: frame={target_frame}, width={target_width}, height={target_height} ===")
    
    with open(file_path, 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节")
    print()
    
    # 方法1: 寻找连续的字节模式
    print("=== 方法1: 寻找连续字节模式 ===")
    pattern_found = False
    
    for offset in range(len(data) - 12):
        # 尝试不同的数据类型组合
        for frame_size in [1, 2, 4]:
            for width_size in [1, 2, 4]:
                for height_size in [1, 2, 4]:
                    try:
                        pos = offset
                        
                        # 读取frame
                        if frame_size == 1:
                            frame = struct.unpack('B', data[pos:pos+1])[0]
                        elif frame_size == 2:
                            frame = struct.unpack('<H', data[pos:pos+2])[0]
                        else:
                            frame = struct.unpack('<I', data[pos:pos+4])[0]
                        pos += frame_size
                        
                        # 读取width
                        if width_size == 1:
                            width = struct.unpack('B', data[pos:pos+1])[0]
                        elif width_size == 2:
                            width = struct.unpack('<H', data[pos:pos+2])[0]
                        else:
                            width = struct.unpack('<I', data[pos:pos+4])[0]
                        pos += width_size
                        
                        # 读取height
                        if height_size == 1:
                            height = struct.unpack('B', data[pos:pos+1])[0]
                        elif height_size == 2:
                            height = struct.unpack('<H', data[pos:pos+2])[0]
                        else:
                            height = struct.unpack('<I', data[pos:pos+4])[0]
                        
                        if frame == target_frame and width == target_width and height == target_height:
                            print(f"*** 找到匹配! ***")
                            print(f"  偏移: {offset} (0x{offset:04X})")
                            print(f"  数据类型: frame={frame_size}字节, width={width_size}字节, height={height_size}字节")
                            print(f"  值: frame={frame}, width={width}, height={height}")
                            
                            # 显示该位置的原始字节
                            end_pos = offset + frame_size + width_size + height_size
                            raw_bytes = data[offset:end_pos]
                            print(f"  原始字节: {' '.join(f'{b:02X}' for b in raw_bytes)}")
                            pattern_found = True
                            
                    except (struct.error, IndexError):
                        continue
    
    if not pattern_found:
        print("未找到连续的字节模式")
    print()
    
    # 方法2: 检查是否是文件指针错误
    print("=== 方法2: 检查文件指针/偏移错误 ===")
    
    # 检查常见的文件指针错误位置
    common_offsets = [
        0,      # 正确位置
        1, 2, 3, 4,  # 小偏移
        8, 12, 16,   # 对齐偏移
        13,     # 跳过色彩模式字段
        341,    # 数据开始位置
        512, 1024, 2048,  # 扇区边界
    ]
    
    for offset in common_offsets:
        if offset + 12 <= len(data):
            try:
                # 尝试从该偏移读取头部
                frame = struct.unpack('<I', data[offset:offset+4])[0]
                width = struct.unpack('<I', data[offset+4:offset+8])[0]
                height = struct.unpack('<I', data[offset+8:offset+12])[0]
                
                print(f"偏移 {offset:4d} (0x{offset:04X}): frame={frame:3d}, width={width:3d}, height={height:3d}")
                
                if frame == target_frame and width == target_width and height == target_height:
                    print(f"  *** 匹配! ESP32S3可能从偏移{offset}开始读取 ***")
                    
            except (struct.error, IndexError):
                print(f"偏移 {offset:4d}: 无法读取")
    print()
    
    # 方法3: 检查是否是数据类型错误
    print("=== 方法3: 检查数据类型错误 ===")
    
    # 从正确位置开始，但使用错误的数据类型
    offset = 0
    print("从正确位置(0)开始，使用不同数据类型:")
    
    for frame_type in ['B', 'H', 'I']:
        for width_type in ['B', 'H', 'I']:
            for height_type in ['B', 'H', 'I']:
                try:
                    pos = offset
                    
                    frame = struct.unpack(f'<{frame_type}', data[pos:pos+struct.calcsize(frame_type)])[0]
                    pos += struct.calcsize(frame_type)
                    
                    width = struct.unpack(f'<{width_type}', data[pos:pos+struct.calcsize(width_type)])[0]
                    pos += struct.calcsize(width_type)
                    
                    height = struct.unpack(f'<{height_type}', data[pos:pos+struct.calcsize(height_type)])[0]
                    
                    type_names = {'B': '1字节', 'H': '2字节', 'I': '4字节'}
                    
                    if frame == target_frame and width == target_width and height == target_height:
                        print(f"*** 匹配! ***")
                        print(f"  数据类型: frame={type_names[frame_type]}, width={type_names[width_type]}, height={type_names[height_type]}")
                        print(f"  值: frame={frame}, width={width}, height={height}")
                        
                except (struct.error, IndexError):
                    continue
    print()
    
    # 方法4: 检查是否是字节序错误
    print("=== 方法4: 检查字节序错误 ===")
    
    for endian in ['<', '>']:
        endian_name = "小端序" if endian == '<' else "大端序"
        try:
            frame = struct.unpack(f'{endian}I', data[0:4])[0]
            width = struct.unpack(f'{endian}I', data[4:8])[0]
            height = struct.unpack(f'{endian}I', data[8:12])[0]
            
            print(f"{endian_name}: frame={frame}, width={width}, height={height}")
            
            if frame == target_frame and width == target_width and height == target_height:
                print(f"  *** 匹配! ESP32S3可能使用了{endian_name} ***")
                
        except (struct.error, IndexError):
            print(f"{endian_name}: 无法读取")
    
    print()
    print("=== 总结 ===")
    print("如果以上方法都没有找到匹配，可能的原因包括:")
    print("1. ESP32S3读取了完全不同的文件")
    print("2. ESP32S3的文件系统损坏了文件")
    print("3. ESP32S3使用了不同的解析逻辑")
    print("4. 报告的数值不是直接从文件头读取的，而是计算得出的")

def main():
    if len(sys.argv) != 2:
        print("用法: python find_error_pattern.py <bin_file>")
        return
    
    find_error_pattern(sys.argv[1])

if __name__ == "__main__":
    main()
