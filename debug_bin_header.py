#!/usr/bin/env python3
"""
调试bin文件头部信息的工具
用于分析ESP32S3解析错误的原因
"""

import struct
import sys
from pathlib import Path

def analyze_bin_header(file_path):
    """分析bin文件头部信息"""
    if not Path(file_path).exists():
        print(f"文件不存在: {file_path}")
        return
    
    with open(file_path, 'rb') as f:
        # 读取前32字节用于分析
        data = f.read(32)
        
    if len(data) < 13:
        print("文件太小，无法读取头部信息")
        return
    
    print(f"=== 分析文件: {file_path} ===")
    print(f"文件大小: {Path(file_path).stat().st_size} bytes")
    print()
    
    # 显示原始字节
    print("原始字节 (前32字节):")
    for i in range(0, min(32, len(data)), 16):
        hex_part = ' '.join(f'{b:02X}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        print(f"{i:08X}: {hex_part:<48} |{ascii_part}|")
    print()
    
    # 小端序解析 (正确的格式)
    print("=== 小端序解析 (正确格式) ===")
    frame_count_le = struct.unpack('<I', data[0:4])[0]
    width_le = struct.unpack('<I', data[4:8])[0]
    height_le = struct.unpack('<I', data[8:12])[0]
    color_mode_le = struct.unpack('<B', data[12:13])[0]
    
    print(f"帧数: {frame_count_le}")
    print(f"宽度: {width_le}")
    print(f"高度: {height_le}")
    print(f"色彩模式: {color_mode_le}")
    print()
    
    # 大端序解析 (错误的格式)
    print("=== 大端序解析 (错误格式) ===")
    frame_count_be = struct.unpack('>I', data[0:4])[0]
    width_be = struct.unpack('>I', data[4:8])[0]
    height_be = struct.unpack('>I', data[8:12])[0]
    color_mode_be = struct.unpack('>B', data[12:13])[0]
    
    print(f"帧数: {frame_count_be}")
    print(f"宽度: {width_be}")
    print(f"高度: {height_be}")
    print(f"色彩模式: {color_mode_be}")
    print()
    
    # 分析可能的偏移错误
    print("=== 可能的偏移错误分析 ===")
    for offset in range(1, 8):
        try:
            shifted_data = data[offset:]
            if len(shifted_data) >= 12:
                frame_count = struct.unpack('<I', shifted_data[0:4])[0]
                width = struct.unpack('<I', shifted_data[4:8])[0]
                height = struct.unpack('<I', shifted_data[8:12])[0]
                
                print(f"偏移 {offset} 字节: 帧数={frame_count}, 宽度={width}, 高度={height}")
                
                # 检查是否匹配ESP32S3的错误结果
                if frame_count == 8 and width == 135 and height == 240:
                    print(f"  *** 匹配ESP32S3错误结果! 偏移量为 {offset} 字节 ***")
        except:
            pass
    print()
    
    # 分析不同数据类型的解析结果
    print("=== 不同数据类型解析分析 ===")
    
    # 尝试用short (2字节) 解析
    print("使用short (2字节) 解析:")
    for i in range(0, min(12, len(data)), 2):
        if i + 2 <= len(data):
            value_le = struct.unpack('<H', data[i:i+2])[0]
            value_be = struct.unpack('>H', data[i:i+2])[0]
            print(f"  位置 {i}: 小端={value_le}, 大端={value_be}")
    print()
    
    # 尝试用byte (1字节) 解析
    print("使用byte (1字节) 解析:")
    for i in range(min(16, len(data))):
        value = struct.unpack('B', data[i:i+1])[0]
        print(f"  位置 {i}: {value}")
    print()
    
    # 检查是否存在特定的错误模式
    print("=== 特定错误模式检查 ===")

    # 检查是否是字节序错误导致的特定数值
    target_frame = 8
    target_width = 135
    target_height = 240

    print(f"寻找可能产生 frame={target_frame}, width={target_width}, height={target_height} 的解析方式:")

    # 尝试各种可能的解析方式
    for offset in range(8):
        for byte_order in ['<', '>']:  # 小端和大端
            for data_size in [1, 2, 4]:  # 1字节、2字节、4字节
                try:
                    shifted_data = data[offset:]
                    if len(shifted_data) >= data_size * 3:
                        if data_size == 1:
                            fmt = f'{byte_order}BBB'
                        elif data_size == 2:
                            fmt = f'{byte_order}HHH'
                        else:
                            fmt = f'{byte_order}III'

                        values = struct.unpack(fmt, shifted_data[:data_size*3])

                        if values[0] == target_frame and values[1] == target_width and values[2] == target_height:
                            endian_name = "小端" if byte_order == '<' else "大端"
                            print(f"  匹配! 偏移={offset}, {endian_name}, {data_size}字节数据类型")

                except:
                    pass

    # ESP32S3特定的问题分析
    print("\n=== ESP32S3特定问题分析 ===")

    # 模拟可能的C结构体对齐问题
    print("可能的C结构体对齐问题:")
    print("如果ESP32S3使用了错误的结构体定义，比如:")
    print("  struct { uint8_t frame; uint8_t width; uint8_t height; } // 错误的1字节类型")
    print("  或者存在内存对齐填充问题")

    # 检查是否是部分字节被当作完整数值
    print("\n检查部分字节解析:")
    if len(data) >= 16:
        # 检查是否某些字节组合能产生目标值
        for i in range(len(data) - 2):
            if data[i] == target_frame:
                print(f"  位置 {i}: 找到 frame={target_frame}")
                if i + 1 < len(data) and data[i + 1] == target_width:
                    print(f"  位置 {i+1}: 找到 width={target_width}")
                    if i + 2 < len(data) and data[i + 2] == target_height:
                        print(f"  位置 {i+2}: 找到 height={target_height}")
                        print(f"  *** 可能的问题: ESP32S3将每个值当作1字节读取，偏移={i} ***")

    # 检查文件损坏的可能性
    print("\n文件完整性检查:")
    expected_header_size = 13 + frame_count_le * 8
    print(f"期望的头部大小: {expected_header_size} 字节")
    print(f"实际文件大小: {Path(file_path).stat().st_size} 字节")

    if Path(file_path).stat().st_size < expected_header_size:
        print("  *** 警告: 文件可能被截断! ***")

    # 生成ESP32S3调试建议
    print("\n=== ESP32S3调试建议 ===")
    print("1. 检查文件传输是否完整:")
    print("   - 验证文件大小是否正确")
    print("   - 检查文件的前16字节是否为: 29 00 00 00 CB 00 00 00 68 01 00 00 02")
    print()
    print("2. 检查C代码的数据类型定义:")
    print("   - 确保使用 uint32_t 读取帧数、宽度、高度")
    print("   - 确保使用 uint8_t 读取色彩模式")
    print("   - 确保使用小端序读取")
    print()
    print("3. 检查内存对齐:")
    print("   - 使用 __attribute__((packed)) 避免结构体填充")
    print("   - 或者逐个字段读取而不是整个结构体读取")
    print()
    print("4. 示例C代码:")
    print("   uint32_t frame_count, width, height;")
    print("   uint8_t color_mode;")
    print("   fread(&frame_count, 4, 1, file);  // 小端序")
    print("   fread(&width, 4, 1, file);")
    print("   fread(&height, 4, 1, file);")
    print("   fread(&color_mode, 1, 1, file);")

def main():
    if len(sys.argv) != 2:
        print("用法: python debug_bin_header.py <bin_file>")
        print("示例: python debug_bin_header.py output_png_sequence.bin")
        return
    
    analyze_bin_header(sys.argv[1])

if __name__ == "__main__":
    main()
