package org.example;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.List;

/**
 * Unit test for simple App.
 */
public class AppTest
{

    @Test
    public void c() {

        // Good o = Proxy.newProxyInstance(newProxyInstanceClassLoader.getSystemClassLoader(), GoodsI.class, new JdkInvocationHandler());
        //
        // List<Good> goo = (List<Good>) ProxyUtils.getUserClass(List.class);
        String json = "{\"goodList:\":{}}";
        Goods goods = JSONObject.parseObject(json, Goods.class);

        CollectionUtils.isEmpty(goods.goodList);
    }


    public class JdkInvocationHandler implements InvocationHandler {

        /**
         *
         * @param proxy 代理对象
         * @param method 目标对象方法
         * @param args 目标对象参数
         * @return
         * @throws Throwable
         */
        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            return method.invoke(new Goods(), args);
        }
    }

    interface GoodsI{

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Goods implements GoodsI {
        private List<Good> goodList;
    }

    @Data
    @Builder
    public static class Good {
        private Long id;
    }
}
