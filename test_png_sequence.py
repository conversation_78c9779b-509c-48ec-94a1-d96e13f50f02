#!/usr/bin/env python3
"""
Test script for PNG Sequence to Binary Converter
Creates test PNG sequence and verifies the conversion works correctly
"""

import os
import shutil
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw
from png_sequence_to_bin import (
    convert_png_sequence_to_bin_data,
    ColorMode,
    read_png_sequence_frames,
    logger
)


def create_test_png_sequence(output_dir: str, num_frames: int = 5) -> str:
    """Create a test PNG sequence with transparency for testing"""
    os.makedirs(output_dir, exist_ok=True)
    
    for i in range(num_frames):
        # Create RGBA image with transparency
        img = Image.new('RGBA', (64, 64), (0, 0, 0, 0))  # Transparent background
        draw = ImageDraw.Draw(img)
        
        # Draw a moving colored circle with transparency
        x = i * 8
        y = 32
        radius = 10
        
        # Semi-transparent colored circle
        color = (255, i * 50, 255 - i * 50, 180)
        draw.ellipse([x, y, x + radius * 2, y + radius * 2], fill=color)
        
        # Add some text with transparency
        draw.text((5, 5), f"Frame {i}", fill=(255, 255, 255, 200))
        
        # Save with proper naming convention
        filename = f"frame_{i:06d}.png"
        img.save(os.path.join(output_dir, filename))
    
    logger.info(f"Created {num_frames} test PNG frames in {output_dir}")
    return output_dir


def test_basic_conversion():
    """Test basic PNG sequence conversion"""
    print("Testing basic PNG sequence conversion...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test PNG sequence
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 5)
        
        # Test conversion
        bin_data = convert_png_sequence_to_bin_data(
            png_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        # Verify output
        assert len(bin_data) > 0, "Binary data should not be empty"
        
        # Check header format (first 13 bytes)
        import struct
        frames, width, height, color_mode_val = struct.unpack('<IIIB', bin_data[:13])
        
        assert frames == 5, f"Expected 5 frames, got {frames}"
        assert width == 64, f"Expected width 64, got {width}"
        assert height == 64, f"Expected height 64, got {height}"
        assert color_mode_val == 2, f"Expected color mode 2 (RGB565A8), got {color_mode_val}"
        
        print("✓ Basic conversion test passed")


def test_color_modes():
    """Test different color modes"""
    print("Testing different color modes...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 3)
        
        expected_sizes = {
            ColorMode.RGB565: 64 * 64 * 2,    # 2 bytes per pixel
            ColorMode.RGB565A8: 64 * 64 * 3,  # 3 bytes per pixel
            ColorMode.ARGB8888: 64 * 64 * 4   # 4 bytes per pixel
        }
        
        for color_mode in [ColorMode.RGB565, ColorMode.RGB565A8, ColorMode.ARGB8888]:
            bin_data = convert_png_sequence_to_bin_data(
                png_dir,
                color_mode=color_mode,
                enable_compression=False  # Disable compression for size testing
            )
            
            # Check header
            import struct
            frames, width, height, color_mode_val = struct.unpack('<IIIB', bin_data[:13])
            assert color_mode_val == color_mode.value
            
            # Calculate expected frame data size
            expected_frame_size = expected_sizes[color_mode]
            header_size = 13 + frames * 8  # Header + frame index
            expected_total_size = header_size + frames * expected_frame_size
            
            assert len(bin_data) == expected_total_size, \
                f"Color mode {color_mode.name}: expected {expected_total_size} bytes, got {len(bin_data)}"
        
        print("✓ Color modes test passed")


def test_compression():
    """Test compression functionality"""
    print("Testing compression...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 5)
        
        # Test with compression
        compressed_data = convert_png_sequence_to_bin_data(
            png_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=True
        )
        
        # Test without compression
        uncompressed_data = convert_png_sequence_to_bin_data(
            png_dir,
            color_mode=ColorMode.RGB565A8,
            enable_compression=False
        )
        
        # Compressed should be smaller (for most real images)
        # Note: For very small test images, compression might not help much
        assert len(compressed_data) > 0, "Compressed data should not be empty"
        assert len(uncompressed_data) > 0, "Uncompressed data should not be empty"
        
        # Headers should be identical
        assert compressed_data[:13] == uncompressed_data[:13], "Headers should be identical"
        
        print(f"✓ Compression test passed (compressed: {len(compressed_data)} bytes, "
              f"uncompressed: {len(uncompressed_data)} bytes)")


def test_frame_operations():
    """Test frame resizing and limiting"""
    print("Testing frame operations...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 10)
        
        # Test frame limiting
        limited_data = convert_png_sequence_to_bin_data(
            png_dir,
            max_frames=5,
            color_mode=ColorMode.RGB565A8,
            enable_compression=False
        )
        
        import struct
        frames, width, height, _ = struct.unpack('<IIIB', limited_data[:13])
        assert frames == 5, f"Expected 5 frames after limiting, got {frames}"
        
        # Test resizing
        resized_data = convert_png_sequence_to_bin_data(
            png_dir,
            width=32,
            height=32,
            color_mode=ColorMode.RGB565A8,
            enable_compression=False
        )
        
        frames, width, height, _ = struct.unpack('<IIIB', resized_data[:13])
        assert width == 32, f"Expected width 32 after resizing, got {width}"
        assert height == 32, f"Expected height 32 after resizing, got {height}"
        
        print("✓ Frame operations test passed")


def test_png_reading():
    """Test PNG sequence reading functionality"""
    print("Testing PNG sequence reading...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 3)
        
        # Test reading frames
        frames = read_png_sequence_frames(png_dir)
        
        assert len(frames) == 3, f"Expected 3 frames, got {len(frames)}"
        
        for frame in frames:
            assert frame.mode == 'RGBA', f"Expected RGBA mode, got {frame.mode}"
            assert frame.size == (64, 64), f"Expected size (64, 64), got {frame.size}"
        
        print("✓ PNG reading test passed")


def test_error_handling():
    """Test error handling"""
    print("Testing error handling...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test non-existent directory
        try:
            convert_png_sequence_to_bin_data("non_existent_dir")
            assert False, "Should have raised an exception for non-existent directory"
        except IOError:
            pass  # Expected
        
        # Test empty directory
        empty_dir = os.path.join(temp_dir, "empty")
        os.makedirs(empty_dir)
        
        try:
            convert_png_sequence_to_bin_data(empty_dir)
            assert False, "Should have raised an exception for empty directory"
        except IOError:
            pass  # Expected
        
        print("✓ Error handling test passed")


def run_all_tests():
    """Run all tests"""
    print("Running PNG Sequence to Binary Converter Tests")
    print("=" * 50)
    
    try:
        test_basic_conversion()
        test_color_modes()
        test_compression()
        test_frame_operations()
        test_png_reading()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
