import argparse
from pathlib import Path
import os
from PIL import Image, ImageSequence
import struct

# 2 字节，RGB565，不带 alpha 通道
def convert_to_rgb565(img: Image) -> bytearray:
    """将 PIL 图像转为 RGB565 格式字节流（小端）"""
    rgb565_data = bytearray()
    rgb_img = img.convert("RGB")
    pixels = rgb_img.getdata()
    for r, g, b in pixels:
        pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3)
        rgb565_data.extend(pixel.to_bytes(2, "little"))
    return rgb565_data

# 3 字节，RGB565 + A8，带 alpha 通道
def convert_to_rgb565a8(img: Image) -> bytearray:
    """将 PIL 图像转为 LVGL 定义的 RGB565A8"""
    img = img.convert("RGBA")
    output = bytearray()

    for r, g, b, a in img.getdata():
        pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3)
        output.extend(pixel.to_bytes(2, "little"))
        output.extend(a.to_bytes(1, "little"))
    return output

# 4 字节，RGBA，带 alpha 通道
def convert_to_argb8888(img: Image) -> bytearray:
    argb_data = bytearray()
    rgba_img = img.convert("RGBA")
    pixels = rgba_img.getdata()
    for r, g, b, a in pixels:
        argb_data.extend([b, g, r, a])
    return argb_data

def gif_to_bin(gif_path: str, output_bin: str, width: int = None, height: int = None, max_frames: int = None) -> None:
    """
    将 GIF 转为带帧索引结构的 RGB565 .bin 文件（可选限制帧数）
    """
    with Image.open(gif_path) as img:
        frames = []
        frame_count = 0
        for frame in ImageSequence.Iterator(img):
            if width and height:
                frame = frame.resize((width, height))
            frames.append(frame.copy())
            frame_count += 1

    print(f"原始帧数: {frame_count}")

    # 控制最大帧数（帧降采样）
    if max_frames and len(frames) > max_frames:
        skip_factor = len(frames) // max_frames + 1
        frames = frames[::skip_factor]
    print(f"输出帧数: {len(frames)}")

    index_data = bytearray()
    frame_data = bytearray()

    # 关键头信息
    # 写入总帧数
    index_data.extend(struct.pack("I", len(frames)))
    # 写入分辨率 width 和 height
    index_data.extend(struct.pack("I", width))
    index_data.extend(struct.pack("I", height))

    # 第一个数据帧的位置：前 12 字节存储帧数 + 宽度 + 高度，最后加上每帧索引：起始位置 + 长度
    data_offset = 12 + len(frames) * 8

    for frame in frames:
        image_frame = convert_to_rgb565a8(frame)
        # 写入当前帧起始位置和长度
        index_data.extend(struct.pack("I", data_offset))
        index_data.extend(struct.pack("I", len(image_frame)))
        # 单独存放图像数据
        frame_data.extend(image_frame)
        data_offset += len(image_frame)

    with open(output_bin, "wb") as f:
        f.write(index_data)
        f.write(frame_data)

    print(f"转换完成 → {output_bin}")
    print(f"输出大小: {os.path.getsize(output_bin) / 1024:.2f} KB")

def main():
    parser = argparse.ArgumentParser(description="将 GIF 转为 RGB565 格式的 bin 文件")
    parser.add_argument("input", help="输入 GIF 文件路径")
    parser.add_argument("--width", type=int, default=None, help="输出宽度（像素）")
    parser.add_argument("--height", type=int, default=None, help="输出高度（像素）")
    parser.add_argument("--max_frames", type=int, default=None, help="最大帧数（可选）")
    args = parser.parse_args()

    output_bin = Path(args.input).stem + ".bin"
    gif_to_bin(args.input, output_bin, args.width, args.height, args.max_frames)

if __name__ == "__main__":
    main()
