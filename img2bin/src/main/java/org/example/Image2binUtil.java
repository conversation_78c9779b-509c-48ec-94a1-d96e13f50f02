package org.example;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;


public class Image2binUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(Image2binUtil.class);

    /**
     * 色彩解码模式
     */
    public enum ColorMode {
        /*CG大图*/RGB565((img) -> {
            //2Bytes，RGB565，不带alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            ByteBuffer buffer = ByteBuffer.allocate(width * height * 2);
            buffer.order(ByteOrder.LITTLE_ENDIAN);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int rgb = img.getRGB(x, y);
                    int r = (rgb >> 16) & 0xFF;
                    int g = (rgb >> 8) & 0xFF;
                    int b = rgb & 0xFF;

                    // 转换为RGB565格式
                    int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
                    buffer.putShort((short) pixel);
                }
            }
            return buffer.array();
        }),
        /*人物图（背景透明）*/RGB565A8((img) -> {
            //3 Bytes，RGB565 + A8 +alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            ByteBuffer buffer = ByteBuffer.allocate(width * height * 3);
            buffer.order(ByteOrder.LITTLE_ENDIAN); //小端序
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int argb = img.getRGB(x, y);
                    int a = (argb >> 24) & 0xFF;
                    int r = (argb >> 16) & 0xFF;
                    int g = (argb >> 8) & 0xFF;
                    int b = argb & 0xFF;

                    int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);  //RGB565
                    buffer.putShort((short) pixel);
                    buffer.put((byte) a); //Alpha
                }
            }
            return buffer.array();
        }),
        ARGB8888((img) -> {
            //4Bytes，RGBA，带alpha通道
            int width = img.getWidth();
            int height = img.getHeight();
            byte[] data = new byte[width * height * 4];
            int index = 0;
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int argb = img.getRGB(x, y);
                    int a = (argb >> 24) & 0xFF;
                    int r = (argb >> 16) & 0xFF;
                    int g = (argb >> 8) & 0xFF;
                    int b = argb & 0xFF;

                    // BGRA顺序（小端序）
                    data[index++] = (byte) b;
                    data[index++] = (byte) g;
                    data[index++] = (byte) r;
                    data[index++] = (byte) a;
                }
            }
            return data;
        });

        private final Function<BufferedImage, byte[]> converter;

        ColorMode(Function<BufferedImage, byte[]> converter) {
            this.converter = converter;
        }

        public byte[] convert(BufferedImage image) {
            return converter.apply(image);
        }
    }

    /**
     * 转换GIF文件为二进制数据
     * - 需要写入文件请使用：
     * try (OutputStream os = Files.newOutputStream(new File(outputPath).toPath(), StandardOpenOption.CREATE, StandardOpenOption.DELETE_ON_CLOSE);) {
     * os.write(binData);
     * }
     *
     * @param gifFile
     * @return {@link byte[] }
     * @throws IOException ioexception
     */
    public static byte[] convertGif2BinData(File gifFile, ColorMode colorMode) {
        try {
            return convertGif2BinData(gifFile, null, null, null, colorMode, true);
        } catch (IOException e) {
            LOGGER.error("Image2binUtil.convertGif2BinData {}.转换GIF文件失败: {}", gifFile.getName(), e.getMessage());
            return null;    // passed exception without handling;
        }
    }

    public static byte[] convertGif2BinData(File gifFile, Integer width, Integer height, Integer maxFrames, ColorMode colorMode, boolean enableCompression) throws IOException {
        List<BufferedImage> frames = Image2binUtil.readGifFrames(gifFile);
        //重绘画面
        if (Objects.isNull(width) || Objects.isNull(height)) {
            BufferedImage firstFrame = frames.get(0);
            width = firstFrame.getWidth();
            height = firstFrame.getHeight();
        } else {
            for (int i = 0; i < frames.size(); ++i) {
                BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = resized.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.drawImage(frames.get(i), 0, 0, width, height, null);
                g2d.dispose();
                frames.set(i, resized);
            }
        }
        // 帧降采样
        if (!Objects.isNull(maxFrames) && frames.size() > maxFrames) {
            List<BufferedImage> sampledFrames = new ArrayList<>();
            int skipFactor = frames.size() / maxFrames + 1;
            for (int i = 0; i < frames.size(); i += skipFactor) {
                sampledFrames.add(frames.get(i));
            }
            frames = sampledFrames;
        }

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             DataOutputStream dos = new DataOutputStream(baos)) {
            /*Tip：设备端（宇敏）定制的头信息，皆使用小端序：
                总帧数(4),
                分辨率-宽(4),
                分辨率-高(4),
                色彩模式-(1), //1-RGB565, 2-RGB565A8
                每数据帧的起始位置和大小(8),
                数据帧
            */
            dos.writeInt(Integer.reverseBytes(frames.size()));
            dos.writeInt(Integer.reverseBytes(width));
            dos.writeInt(Integer.reverseBytes(height));
            dos.writeByte(colorMode.ordinal() + 1);

            int dataOffset = 13 + frames.size() * 8;
            // 准备帧数据
            List<byte[]> frameDataList = new ArrayList<>();
            long totalOriginalSize = 0;
            long totalCompressedSize = 0;

            for (BufferedImage frame : frames) {
                byte[] originalFrameData = colorMode.convert(frame);
                totalOriginalSize += originalFrameData.length;
                byte[] frameData;
                if (enableCompression) {
                    frameData = Image2binUtil.compressDataUsedZlib(originalFrameData);
                } else {
                    frameData = originalFrameData;
                }
                totalCompressedSize += frameData.length;
                frameDataList.add(frameData);

                dos.writeInt(Integer.reverseBytes(dataOffset));
                dos.writeInt(Integer.reverseBytes(frameData.length));
                dataOffset += frameData.length;
            }
            for (byte[] frameData : frameDataList) {
                dos.write(frameData);
            }
            dos.flush();
            byte[] result = baos.toByteArray();
            Function<Double, String> formatLogPrecision = v -> String.format("%.2f", v);
            if (enableCompression) {
                double compressionRatio = totalOriginalSize > 0 ? (double) totalCompressedSize / totalOriginalSize : 1.0;
                LOGGER.info("Image2binUtil.convertGif2BinData.output {}.生成二进制数据大小: {} KB, 输出帧数:{}, 压缩比: {}% (原始: {} KB -> 压缩后: {} KB)",
                        gifFile.getName(), formatLogPrecision.apply(result.length / 1024.0), frames.size(), formatLogPrecision.apply(compressionRatio * 100), formatLogPrecision.apply(totalOriginalSize / 1024.0), formatLogPrecision.apply(totalCompressedSize / 1024.0));
            } else {
                LOGGER.info("Image2binUtil.convertGif2BinData.output {}.生成二进制数据大小: {} KB, 输出帧数:{} (未压缩)",
                        gifFile.getName(), formatLogPrecision.apply(result.length / 1024.0), frames.size());
            }
            return result;
        }
    }

    public static List<BufferedImage> readGifFrames(File gifFile) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();
        ImageReader reader = null;
        try (ImageInputStream input = ImageIO.createImageInputStream(gifFile)) {
            Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName("gif");
            if (!readers.hasNext()) {
                throw new IOException("No GIF readers found");
            }
            reader = readers.next();
            reader.setInput(input);
            int numFrames = reader.getNumImages(true);
            ;
            for (int i = 0; i < numFrames; ++i) {
                try {
                    BufferedImage frame = reader.read(i);
                    if (Objects.isNull(frame)) {
                        continue;
                    }
                    BufferedImage convertedFrame = new BufferedImage(frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = convertedFrame.createGraphics();
                    g2d.drawImage(frame, 0, 0, null);
                    g2d.dispose();
                    frames.add(convertedFrame);
                } catch (IndexOutOfBoundsException e) {
                    break;
                } catch (Exception e) {
                    LOGGER.error("Image2binUtil.readGifFrames {}.跳过损坏的第[{}]帧: {}", gifFile.getName(), i, e.getMessage());
                    continue;
                }
            }
        } catch (Exception e) {
            LOGGER.error("Image2binUtil.readGifFrames {}.读取GIF文件失败: {}", gifFile.getName(), e.getMessage());
        } finally {
            if (null != reader) {
                reader.dispose();
            }
        }
        if (frames.isEmpty()) {
            LOGGER.error("Image2binUtil.readGifFrames {}.无法从GIF文件中读取任何有效帧", gifFile.getName());
        }
        return frames;
    }

    /**
     * 使用zlib压缩数据
     *
     * @param data
     * @return
     * @throws IOException
     */
    private static byte[] compressDataUsedZlib(byte[] data) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             DeflaterOutputStream deflaterStream = new DeflaterOutputStream(baos, new Deflater(6))) {
            deflaterStream.write(data);
            deflaterStream.finish();
            return baos.toByteArray();
        }
    }

}
