package org.example;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 二进制文件分析工具
 * 用于分析和对比不同版本生成的bin文件差异
 */
public class BinFileAnalyzer {
    
    public static class BinFileInfo {
        public int frameCount;
        public int width;
        public int height;
        public int colorMode;
        public boolean hasColorModeField;
        public long fileSize;
        public int headerSize;
        public int dataOffset;
        
        @Override
        public String toString() {
            return String.format(
                "BinFileInfo{\n" +
                "  frameCount=%d,\n" +
                "  width=%d,\n" +
                "  height=%d,\n" +
                "  colorMode=%d (%s),\n" +
                "  hasColorModeField=%s,\n" +
                "  fileSize=%d bytes (%.2f KB),\n" +
                "  headerSize=%d,\n" +
                "  dataOffset=%d\n" +
                "}",
                frameCount, width, height, colorMode, getColorModeName(colorMode),
                hasColorModeField, fileSize, fileSize / 1024.0, headerSize, dataOffset
            );
        }
        
        private String getColorModeName(int colorMode) {
            switch (colorMode) {
                case 1: return "RGB565";
                case 2: return "RGB565A8";
                case 3: return "ARGB8888";
                default: return "Unknown";
            }
        }
    }
    
    /**
     * 分析bin文件结构
     */
    public static BinFileInfo analyzeBinFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("文件不存在: " + filePath);
        }
        
        BinFileInfo info = new BinFileInfo();
        info.fileSize = file.length();
        
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] headerBytes = new byte[16]; // 读取足够的头部数据
            int bytesRead = fis.read(headerBytes);
            
            if (bytesRead < 12) {
                throw new IOException("文件太小，无法读取头部信息");
            }
            
            ByteBuffer buffer = ByteBuffer.wrap(headerBytes);
            buffer.order(ByteOrder.LITTLE_ENDIAN);
            
            // 读取基本信息
            info.frameCount = buffer.getInt();
            info.width = buffer.getInt();
            info.height = buffer.getInt();
            
            // 检查是否有色彩模式字段
            if (bytesRead >= 13) {
                info.colorMode = buffer.get() & 0xFF;
                info.hasColorModeField = true;
                info.headerSize = 13 + info.frameCount * 8;
                info.dataOffset = info.headerSize;
            } else {
                info.colorMode = 0; // 未知
                info.hasColorModeField = false;
                info.headerSize = 12 + info.frameCount * 8;
                info.dataOffset = info.headerSize;
            }
        }
        
        return info;
    }
    
    /**
     * 对比两个bin文件
     */
    public static void compareBinFiles(String file1Path, String file2Path) {
        try {
            System.out.println("=== 二进制文件对比分析 ===\n");
            
            BinFileInfo info1 = analyzeBinFile(file1Path);
            BinFileInfo info2 = analyzeBinFile(file2Path);
            
            System.out.println("文件1: " + file1Path);
            System.out.println(info1);
            System.out.println();
            
            System.out.println("文件2: " + file2Path);
            System.out.println(info2);
            System.out.println();
            
            System.out.println("=== 差异分析 ===");
            
            if (info1.frameCount != info2.frameCount) {
                System.out.println("❌ 帧数不同: " + info1.frameCount + " vs " + info2.frameCount);
            } else {
                System.out.println("✅ 帧数相同: " + info1.frameCount);
            }
            
            if (info1.width != info2.width || info1.height != info2.height) {
                System.out.println("❌ 分辨率不同: " + info1.width + "x" + info1.height + 
                                 " vs " + info2.width + "x" + info2.height);
            } else {
                System.out.println("✅ 分辨率相同: " + info1.width + "x" + info1.height);
            }
            
            if (info1.hasColorModeField != info2.hasColorModeField) {
                System.out.println("❌ 色彩模式字段存在性不同: " + info1.hasColorModeField + " vs " + info2.hasColorModeField);
            } else if (info1.hasColorModeField && info1.colorMode != info2.colorMode) {
                System.out.println("❌ 色彩模式不同: " + info1.colorMode + " vs " + info2.colorMode);
            } else if (info1.hasColorModeField) {
                System.out.println("✅ 色彩模式相同: " + info1.colorMode);
            } else {
                System.out.println("⚠️  两个文件都没有色彩模式字段");
            }
            
            if (info1.headerSize != info2.headerSize) {
                System.out.println("❌ 头部大小不同: " + info1.headerSize + " vs " + info2.headerSize);
            } else {
                System.out.println("✅ 头部大小相同: " + info1.headerSize);
            }
            
            long sizeDiff = Math.abs(info1.fileSize - info2.fileSize);
            if (sizeDiff > 0) {
                System.out.println("❌ 文件大小不同: " + info1.fileSize + " vs " + info2.fileSize + 
                                 " (差异: " + sizeDiff + " bytes, " + String.format("%.2f", sizeDiff / 1024.0) + " KB)");
            } else {
                System.out.println("✅ 文件大小相同: " + info1.fileSize);
            }
            
            // 分析可能的原因
            System.out.println("\n=== 可能的差异原因 ===");
            if (!info1.hasColorModeField && info2.hasColorModeField) {
                System.out.println("• 文件1使用旧版本格式（缺少色彩模式字段）");
                System.out.println("• 文件2使用新版本格式（包含色彩模式字段）");
                System.out.println("• 这会导致1字节的头部差异");
            } else if (info1.hasColorModeField && !info2.hasColorModeField) {
                System.out.println("• 文件2使用旧版本格式（缺少色彩模式字段）");
                System.out.println("• 文件1使用新版本格式（包含色彩模式字段）");
                System.out.println("• 这会导致1字节的头部差异");
            }
            
            if (sizeDiff > 1) {
                System.out.println("• 除了头部格式差异外，还可能存在：");
                System.out.println("  - 压缩算法差异");
                System.out.println("  - 图像处理方式差异");
                System.out.println("  - 色彩转换精度差异");
            }
            
        } catch (IOException e) {
            System.err.println("分析文件时发生错误: " + e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        if (args.length == 1) {
            // 单文件分析模式
            try {
                System.out.println("=== 二进制文件分析 ===\n");
                BinFileInfo info = analyzeBinFile(args[0]);
                System.out.println("文件: " + args[0]);
                System.out.println(info);

                // 显示原始字节信息用于调试
                System.out.println("\n=== 原始字节信息 (前16字节) ===");
                showRawBytes(args[0]);

            } catch (IOException e) {
                System.err.println("分析文件时发生错误: " + e.getMessage());
            }
        } else if (args.length == 2) {
            // 双文件对比模式
            compareBinFiles(args[0], args[1]);
        } else {
            System.out.println("用法: ");
            System.out.println("  单文件分析: java BinFileAnalyzer <file>");
            System.out.println("  双文件对比: java BinFileAnalyzer <file1> <file2>");
            System.out.println("示例: java BinFileAnalyzer output_png_sequence.bin");
        }
    }

    /**
     * 显示文件的原始字节信息用于调试
     */
    public static void showRawBytes(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            byte[] bytes = new byte[16];
            int bytesRead = fis.read(bytes);

            System.out.print("十六进制: ");
            for (int i = 0; i < bytesRead; i++) {
                System.out.printf("%02X ", bytes[i] & 0xFF);
            }
            System.out.println();

            // 解析小端序整数
            if (bytesRead >= 12) {
                ByteBuffer buffer = ByteBuffer.wrap(bytes);
                buffer.order(ByteOrder.LITTLE_ENDIAN);

                int frameCount = buffer.getInt();
                int width = buffer.getInt();
                int height = buffer.getInt();

                System.out.println("小端序解析:");
                System.out.println("  帧数: " + frameCount + " (0x" + Integer.toHexString(frameCount) + ")");
                System.out.println("  宽度: " + width + " (0x" + Integer.toHexString(width) + ")");
                System.out.println("  高度: " + height + " (0x" + Integer.toHexString(height) + ")");

                if (bytesRead >= 13) {
                    int colorMode = buffer.get() & 0xFF;
                    System.out.println("  色彩模式: " + colorMode + " (0x" + Integer.toHexString(colorMode) + ")");
                }

                // 同时显示大端序解析结果用于对比
                buffer.rewind();
                buffer.order(ByteOrder.BIG_ENDIAN);
                int frameCountBE = buffer.getInt();
                int widthBE = buffer.getInt();
                int heightBE = buffer.getInt();

                System.out.println("大端序解析 (用于对比):");
                System.out.println("  帧数: " + frameCountBE + " (0x" + Integer.toHexString(frameCountBE) + ")");
                System.out.println("  宽度: " + widthBE + " (0x" + Integer.toHexString(widthBE) + ")");
                System.out.println("  高度: " + heightBE + " (0x" + Integer.toHexString(heightBE) + ")");
            }
        }
    }
}
