package org.example;

import junit.framework.TestCase;
import java.awt.image.BufferedImage;
import java.awt.Graphics2D;
import java.awt.Color;
import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import javax.imageio.ImageIO;

/**
 * 测试Image2binUtil的压缩功能
 */
public class Image2binUtilCompressionTest extends TestCase {

    /**
     * 创建测试用的简单图像
     */
    private BufferedImage createTestImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        // 创建简单的渐变图案，便于压缩
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int red = (x * 255) / width;
                int green = (y * 255) / height;
                int blue = 128;
                g2d.setColor(new Color(red, green, blue));
                g2d.fillRect(x, y, 1, 1);
            }
        }
        
        g2d.dispose();
        return image;
    }

    /**
     * 创建测试用的GIF文件
     */
    private File createTestGif() throws IOException {
        File tempFile = File.createTempFile("test", ".gif");
        tempFile.deleteOnExit();
        
        BufferedImage image = createTestImage(64, 64);
        ImageIO.write(image, "gif", tempFile);
        
        return tempFile;
    }

    /**
     * 测试压缩功能基本工作
     */
    public void testCompressionBasicFunctionality() throws IOException {
        File testGif = createTestGif();
        
        // 测试未压缩版本
        byte[] uncompressedData = Image2binUtil.convertGif2BinData(
            testGif, 32, 32, null, Image2binUtil.ColorMode.RGB565, false);
        
        // 测试压缩版本
        byte[] compressedData = Image2binUtil.convertGif2BinData(
            testGif, 32, 32, null, Image2binUtil.ColorMode.RGB565, true);
        
        // 验证数据不为空
        assertNotNull("未压缩数据不应为空", uncompressedData);
        assertNotNull("压缩数据不应为空", compressedData);
        
        // 验证压缩版本通常应该更小（对于有规律的图像）
        assertTrue("压缩版本应该更小或相等", compressedData.length <= uncompressedData.length);
        
        System.out.println("未压缩大小: " + uncompressedData.length + " 字节");
        System.out.println("压缩大小: " + compressedData.length + " 字节");
        
        if (uncompressedData.length > 0) {
            double ratio = (double) compressedData.length / uncompressedData.length;
            System.out.println("压缩比: " + String.format("%.2f%%", ratio * 100));
        }
    }

    /**
     * 测试压缩标志位是否正确设置
     */
    public void testCompressionFlag() throws IOException {
        File testGif = createTestGif();
        
        // 测试未压缩版本
        byte[] uncompressedData = Image2binUtil.convertGif2BinData(
            testGif, 32, 32, null, Image2binUtil.ColorMode.RGB565, false);
        
        // 测试压缩版本
        byte[] compressedData = Image2binUtil.convertGif2BinData(
            testGif, 32, 32, null, Image2binUtil.ColorMode.RGB565, true);
        
        // 验证头部长度
        assertTrue("未压缩数据头部长度应该至少13字节", uncompressedData.length >= 13);
        assertTrue("压缩数据头部长度应该至少13字节", compressedData.length >= 13);
        
        // 检查压缩标志位
        int uncompressedFlag = uncompressedData[13] & 0xFF;
        int compressedFlag = compressedData[13] & 0xFF;
        
        assertEquals("未压缩版本的压缩标志应为0", 0, uncompressedFlag);
        assertEquals("压缩版本的压缩标志应为1", 1, compressedFlag);
        
        System.out.println("未压缩标志: " + uncompressedFlag);
        System.out.println("压缩标志: " + compressedFlag);
    }

    /**
     * 测试不同色彩模式的压缩
     */
    public void testCompressionWithDifferentColorModes() throws IOException {
        File testGif = createTestGif();
        
        Image2binUtil.ColorMode[] modes = {
            Image2binUtil.ColorMode.RGB565,
            Image2binUtil.ColorMode.RGB565A8,
            Image2binUtil.ColorMode.ARGB8888
        };
        
        for (Image2binUtil.ColorMode mode : modes) {
            byte[] uncompressedData = Image2binUtil.convertGif2BinData(
                testGif, 32, 32, null, mode, false);
            
            byte[] compressedData = Image2binUtil.convertGif2BinData(
                testGif, 32, 32, null, mode, true);
            
            assertNotNull("未压缩数据不应为空 (" + mode + ")", uncompressedData);
            assertNotNull("压缩数据不应为空 (" + mode + ")", compressedData);
            
            System.out.println(mode + " - 未压缩: " + uncompressedData.length + 
                             ", 压缩: " + compressedData.length);
        }
    }

    /**
     * 测试头部信息解析
     */
    public void testHeaderParsing() throws IOException {
        File testGif = createTestGif();
        
        byte[] data = Image2binUtil.convertGif2BinData(
            testGif, 32, 32, null, Image2binUtil.ColorMode.RGB565, true);
        
        // 解析头部信息（小端序）
        int frameCount = (data[3] & 0xFF) << 24 | (data[2] & 0xFF) << 16 |
                (data[1] & 0xFF) << 8 | (data[0] & 0xFF);
        int width = (data[7] & 0xFF) << 24 | (data[6] & 0xFF) << 16 |
                (data[5] & 0xFF) << 8 | (data[4] & 0xFF);
        int height = (data[11] & 0xFF) << 24 | (data[10] & 0xFF) << 16 |
                (data[9] & 0xFF) << 8 | (data[8] & 0xFF);
        int colorMode = data[12] & 0xFF;
        int compressionFlag = data[13] & 0xFF;
        
        assertEquals("帧数应为1", 1, frameCount);
        assertEquals("宽度应为32", 32, width);
        assertEquals("高度应为32", 32, height);
        assertEquals("色彩模式应为1 (RGB565)", 1, colorMode);
        assertEquals("压缩标志应为1", 1, compressionFlag);
        
        System.out.println("解析的头部信息:");
        System.out.println("  帧数: " + frameCount);
        System.out.println("  尺寸: " + width + "x" + height);
        System.out.println("  色彩模式: " + colorMode);
        System.out.println("  压缩标志: " + compressionFlag);
    }
}
