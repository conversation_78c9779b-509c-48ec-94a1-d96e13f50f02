#!/usr/bin/env python3
"""
Test script for PNG Sequence Resizer
Tests the resize functionality for PNG sequences
"""

import os
import shutil
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw
import unittest

from resize_png_sequence import (
    resize_png_sequence_files,
    batch_resize_png_sequences,
    get_png_sequence_info
)


def create_test_png_sequence(output_dir: str, num_frames: int = 5, width: int = 100, height: int = 100) -> str:
    """Create a test PNG sequence for testing"""
    os.makedirs(output_dir, exist_ok=True)
    
    for i in range(num_frames):
        # Create RGBA image with transparency
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))  # Transparent background
        draw = ImageDraw.Draw(img)
        
        # Draw a moving colored circle with transparency
        x = i * (width // num_frames)
        y = height // 2
        radius = 15
        
        # Semi-transparent colored circle
        color = (255, i * 50, 255 - i * 50, 180)
        draw.ellipse([x, y, x + radius * 2, y + radius * 2], fill=color)
        
        # Add frame number text
        draw.text((5, 5), f"Frame {i}", fill=(255, 255, 255, 200))
        
        # Save with proper naming convention
        filename = f"frame_{i:06d}.png"
        img.save(os.path.join(output_dir, filename))
    
    return output_dir


class TestResizePngSequence(unittest.TestCase):
    """Test cases for PNG Sequence Resizer"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir)
    
    def test_basic_resize(self):
        """Test basic resize functionality"""
        # Create test PNG sequence
        png_dir = os.path.join(self.temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 5, 100, 100)
        
        # Verify original dimensions
        original_files = list(Path(png_dir).glob("frame_*.png"))
        self.assertEqual(len(original_files), 5)
        
        original_img = Image.open(original_files[0])
        self.assertEqual(original_img.size, (100, 100))
        original_img.close()
        
        # Resize to 64x64
        resize_png_sequence_files(png_dir, 64, 64, backup=True)
        
        # Verify resized dimensions
        resized_files = list(Path(png_dir).glob("frame_*.png"))
        self.assertEqual(len(resized_files), 5)
        
        resized_img = Image.open(resized_files[0])
        self.assertEqual(resized_img.size, (64, 64))
        resized_img.close()
        
        # Verify backup was created
        backup_dir = Path(png_dir) / "backup_original"
        self.assertTrue(backup_dir.exists())
        
        backup_files = list(backup_dir.glob("frame_*.png"))
        self.assertEqual(len(backup_files), 5)
    
    def test_resize_without_backup(self):
        """Test resize without backup"""
        png_dir = os.path.join(self.temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 3, 80, 80)
        
        # Resize without backup
        resize_png_sequence_files(png_dir, 40, 40, backup=False)
        
        # Verify resized dimensions
        resized_files = list(Path(png_dir).glob("frame_*.png"))
        self.assertEqual(len(resized_files), 3)
        
        resized_img = Image.open(resized_files[0])
        self.assertEqual(resized_img.size, (40, 40))
        resized_img.close()
        
        # Verify no backup was created
        backup_dir = Path(png_dir) / "backup_original"
        self.assertFalse(backup_dir.exists())
    
    def test_resize_quality_settings(self):
        """Test different quality settings"""
        qualities = ['nearest', 'bilinear', 'bicubic', 'lanczos']
        
        for quality in qualities:
            # Create test PNG sequence
            png_dir = os.path.join(self.temp_dir, f"test_{quality}")
            create_test_png_sequence(png_dir, 2, 100, 100)
            
            # Resize with specific quality
            resize_png_sequence_files(png_dir, 50, 50, backup=False, quality=quality)
            
            # Verify resized dimensions
            resized_files = list(Path(png_dir).glob("frame_*.png"))
            self.assertEqual(len(resized_files), 2)
            
            resized_img = Image.open(resized_files[0])
            self.assertEqual(resized_img.size, (50, 50))
            resized_img.close()
    
    def test_invalid_quality(self):
        """Test invalid quality parameter"""
        png_dir = os.path.join(self.temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 2, 50, 50)
        
        # Test with invalid quality
        with self.assertRaises(ValueError):
            resize_png_sequence_files(png_dir, 25, 25, quality='invalid_quality')
    
    def test_nonexistent_folder(self):
        """Test non-existent folder"""
        with self.assertRaises(IOError):
            resize_png_sequence_files("non_existent_folder", 100, 100)
    
    def test_empty_folder(self):
        """Test empty folder"""
        empty_dir = os.path.join(self.temp_dir, "empty")
        os.makedirs(empty_dir)
        
        with self.assertRaises(IOError):
            resize_png_sequence_files(empty_dir, 100, 100)
    
    def test_transparency_preservation(self):
        """Test transparency preservation"""
        png_dir = os.path.join(self.temp_dir, "transparent_sequence")
        os.makedirs(png_dir, exist_ok=True)
        
        # Create image with gradient transparency
        img = Image.new('RGBA', (60, 60), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw gradient with varying transparency
        for y in range(60):
            alpha = int(255 * (y / 60))
            color = (255, 100, 100, alpha)
            draw.line([(0, y), (60, y)], fill=color)
        
        # Save test image
        filename = os.path.join(png_dir, "frame_000000.png")
        img.save(filename)
        
        # Resize
        resize_png_sequence_files(png_dir, 30, 30, backup=False)
        
        # Check if transparency is preserved
        resized_img = Image.open(filename)
        self.assertEqual(resized_img.mode, 'RGBA')
        
        # Check if alpha channel is preserved
        has_transparency = False
        for y in range(30):
            for x in range(30):
                _, _, _, a = resized_img.getpixel((x, y))
                if a < 255:
                    has_transparency = True
                    break
            if has_transparency:
                break
        
        self.assertTrue(has_transparency)
        resized_img.close()
    
    def test_batch_resize(self):
        """Test batch resize functionality"""
        base_dir = os.path.join(self.temp_dir, "sequences")
        os.makedirs(base_dir)
        
        # Create multiple PNG sequence folders
        sequences = [
            ("sequence1", 3, 120, 120),
            ("sequence2", 2, 80, 80),
            ("sequence3", 4, 150, 150)
        ]
        
        for name, frames, width, height in sequences:
            seq_dir = os.path.join(base_dir, name)
            create_test_png_sequence(seq_dir, frames, width, height)
        
        # Batch resize
        batch_resize_png_sequences(base_dir, 64, 64, backup=True)
        
        # Verify all sequences were resized
        for name, frames, _, _ in sequences:
            seq_dir = Path(base_dir) / name
            
            # Check resized files
            resized_files = list(seq_dir.glob("frame_*.png"))
            self.assertEqual(len(resized_files), frames)
            
            # Check dimensions
            resized_img = Image.open(resized_files[0])
            self.assertEqual(resized_img.size, (64, 64))
            resized_img.close()
            
            # Check backup
            backup_dir = seq_dir / "backup_original"
            self.assertTrue(backup_dir.exists())
            
            backup_files = list(backup_dir.glob("frame_*.png"))
            self.assertEqual(len(backup_files), frames)
    
    def test_get_png_sequence_info(self):
        """Test get_png_sequence_info function"""
        png_dir = os.path.join(self.temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 5, 100, 100)
        
        # Get info
        info = get_png_sequence_info(png_dir)
        
        # Verify info
        self.assertEqual(info['frame_count'], 5)
        self.assertEqual(info['width'], 100)
        self.assertEqual(info['height'], 100)
        self.assertGreaterEqual(info['total_size_mb'], 0)
        
        # Test empty folder
        empty_dir = os.path.join(self.temp_dir, "empty")
        os.makedirs(empty_dir)
        
        empty_info = get_png_sequence_info(empty_dir)
        self.assertEqual(empty_info['frame_count'], 0)
        self.assertIsNone(empty_info['width'])
        self.assertIsNone(empty_info['height'])


if __name__ == "__main__":
    unittest.main()
