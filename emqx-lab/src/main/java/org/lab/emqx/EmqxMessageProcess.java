package org.lab.emqx;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EmqxMessageProcess {
    @Resource
    private MessageChannel mqttOutboundChannel;

    /**
     * 发送设备指令消息
     */
    public boolean sendDeviceCommand(String deviceNo, String payload) {
        try {
            String topic = String.format("device/dog/%s/cmd", deviceNo);

            Message<String> mqttMessage = MessageBuilder
                    .withPayload(payload)
                    .setHeader(MqttHeaders.TOPIC, topic)
                    .build();

            mqttOutboundChannel.send(mqttMessage);
            log.info("通过EMQX发送设备指令成功，deviceNo: {}, topic: {}", deviceNo, topic);
            return true;
        } catch (Exception e) {
            log.error("通过EMQX发送设备指令失败，deviceNo: {}", deviceNo, e);
            return false;
        }
    }

    /**
     * 消息处理器 - 处理设备状态和响应消息
     */
    @Component
    public static class MqttMessageHandler implements MessageHandler {

        @Override
        public void handleMessage(Message<?> message) throws MessagingException {
            String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
            String payload = (String) message.getPayload();

            log.info("收到MQTT消息，主题: {}, 内容: {}", topic, payload);

            // 根据主题处理不同类型的消息
            if (topic.startsWith("device/") && topic.endsWith("/status")) {
                // 处理设备状态消息
                handleDeviceStatus(topic, payload);
            } else if (topic.startsWith("device/") && topic.endsWith("/response")) {
                // 处理设备响应消息
                handleDeviceResponse(topic, payload);
            }
        }

        private void handleDeviceStatus(String topic, String payload) {
            // 从主题中提取设备ID
            String deviceNo = topic.split("/")[1];
            // 处理设备状态更新
            log.info("处理设备状态更新，deviceNo: {}, 状态: {}", deviceNo, payload);
        }

        private void handleDeviceResponse(String topic, String payload) {
            // 从主题中提取设备ID
            String deviceNo = topic.split("/")[1];
            // 处理设备响应
            log.info("处理设备响应，deviceNo: {}, 响应: {}", deviceNo, payload);
        }
    }
}
