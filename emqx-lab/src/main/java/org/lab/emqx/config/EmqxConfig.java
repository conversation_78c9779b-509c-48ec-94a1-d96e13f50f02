package org.lab.emqx.config;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import java.util.UUID;

@Configuration
public class EmqxConfig {

    @Value("${emqx.broker-url:tcp://localhost:1883}")
    private String brokerUrl;

    @Value("${emqx.client-id:toy-dgw-}")
    private String clientId;

    @Value("${emqx.username:admin}")
    private String username;

    @Value("${emqx.password:public}")
    private String password;

    @Value("${emqx.qos:1}")
    private int qos;

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[] { brokerUrl });
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        options.setCleanSession(true);
        options.setKeepAliveInterval(60);
        options.setConnectionTimeout(30);
        factory.setConnectionOptions(options);
        return factory;
    }

    // 出站通道 - 用于发送消息
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    // 入站通道 - 用于接收消息
    @Bean
    public MessageChannel mqttInboundChannel() {
        return new DirectChannel();
    }

    // 配置出站消息处理器
    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MessageHandler mqttOutbound() {
        String clientIdWithSuffix = clientId + UUID.randomUUID().toString().substring(0, 8);
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientIdWithSuffix, mqttClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultQos(qos);
        messageHandler.setDefaultTopic("device/command");
        return messageHandler;
    }

    // 配置入站消息适配器
    @Bean
    public MessageProducer inbound() {
        String clientIdWithSuffix = clientId + "inbound-" + UUID.randomUUID().toString().substring(0, 8);
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(clientIdWithSuffix, mqttClientFactory(),
                        "device/+/status", "device/+/response");
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(qos);
        adapter.setOutputChannel(mqttInboundChannel());
        return adapter;
    }
}
