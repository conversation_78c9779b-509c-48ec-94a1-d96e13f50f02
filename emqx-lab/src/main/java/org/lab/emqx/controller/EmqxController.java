package org.lab.emqx.controller;

import jakarta.annotation.Resource;
import org.lab.emqx.EmqxMessageProcess;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EmqxController {
    @Resource
    private EmqxMessageProcess emqxMessageProcess;

    @GetMapping("send")
    public void send(@RequestParam String deviceNo, @RequestParam String msg) {
        boolean sent = emqxMessageProcess.sendDeviceCommand(deviceNo, msg);
    }
}
