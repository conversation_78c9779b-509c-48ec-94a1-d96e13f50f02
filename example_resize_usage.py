#!/usr/bin/env python3
"""
Example usage of PNG Sequence Resizer
Demonstrates how to resize PNG sequences and overwrite original files
"""

import os
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw
from resize_png_sequence import (
    resize_png_sequence_files,
    batch_resize_png_sequences,
    get_png_sequence_info
)


def create_test_png_sequence(output_dir: str, num_frames: int = 5, width: int = 100, height: int = 100) -> str:
    """Create a test PNG sequence for demonstration"""
    os.makedirs(output_dir, exist_ok=True)
    
    for i in range(num_frames):
        # Create RGBA image with transparency
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))  # Transparent background
        draw = ImageDraw.Draw(img)
        
        # Draw a moving colored circle with transparency
        x = i * (width // num_frames)
        y = height // 2
        radius = 15
        
        # Semi-transparent colored circle
        color = (255, i * 50, 255 - i * 50, 180)
        draw.ellipse([x, y, x + radius * 2, y + radius * 2], fill=color)
        
        # Add frame number text
        draw.text((5, 5), f"Frame {i}", fill=(255, 255, 255, 200))
        
        # Save with proper naming convention
        filename = f"frame_{i:06d}.png"
        img.save(os.path.join(output_dir, filename))
    
    print(f"Created {num_frames} test PNG frames in {output_dir}")
    return output_dir


def example_basic_resize():
    """Example 1: Basic PNG sequence resize"""
    print("=== Example 1: Basic PNG sequence resize ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test PNG sequence (100x100)
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 5, 100, 100)
        
        # Get original info
        original_info = get_png_sequence_info(png_dir)
        print(f"Original: {original_info['width']}x{original_info['height']}, "
              f"{original_info['frame_count']} frames, "
              f"{original_info['total_size_mb']:.2f} MB")
        
        # Resize to 64x64 with backup
        resize_png_sequence_files(png_dir, 64, 64, backup=True, quality='bilinear')
        
        # Get new info
        new_info = get_png_sequence_info(png_dir)
        print(f"Resized: {new_info['width']}x{new_info['height']}, "
              f"{new_info['frame_count']} frames, "
              f"{new_info['total_size_mb']:.2f} MB")
        
        # Check if backup was created
        backup_dir = Path(png_dir) / "backup_original"
        if backup_dir.exists():
            backup_files = list(backup_dir.glob("frame_*.png"))
            print(f"Backup created with {len(backup_files)} files")


def example_resize_without_backup():
    """Example 2: Resize without creating backup"""
    print("\n=== Example 2: Resize without backup ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test PNG sequence
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 3, 80, 80)
        
        print("Original files:")
        for png_file in sorted(Path(png_dir).glob("frame_*.png")):
            img = Image.open(png_file)
            print(f"  {png_file.name}: {img.size}")
            img.close()
        
        # Resize without backup
        resize_png_sequence_files(png_dir, 32, 32, backup=False, quality='bicubic')
        
        print("After resize:")
        for png_file in sorted(Path(png_dir).glob("frame_*.png")):
            img = Image.open(png_file)
            print(f"  {png_file.name}: {img.size}")
            img.close()
        
        # Check that no backup was created
        backup_dir = Path(png_dir) / "backup_original"
        print(f"Backup folder exists: {backup_dir.exists()}")


def example_different_quality_settings():
    """Example 3: Compare different quality settings"""
    print("\n=== Example 3: Different quality settings ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        qualities = ['nearest', 'bilinear', 'bicubic', 'lanczos']
        
        for quality in qualities:
            # Create separate test sequence for each quality
            png_dir = os.path.join(temp_dir, f"test_{quality}")
            create_test_png_sequence(png_dir, 3, 100, 100)
            
            # Get original size
            original_info = get_png_sequence_info(png_dir)
            original_size = original_info['total_size_mb']
            
            # Resize with specific quality
            resize_png_sequence_files(png_dir, 50, 50, backup=False, quality=quality)
            
            # Get new size
            new_info = get_png_sequence_info(png_dir)
            new_size = new_info['total_size_mb']
            
            print(f"Quality '{quality}': {original_size:.3f} MB -> {new_size:.3f} MB")


def example_batch_resize():
    """Example 4: Batch resize multiple folders"""
    print("\n=== Example 4: Batch resize multiple folders ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create multiple PNG sequence folders
        base_dir = os.path.join(temp_dir, "sequences")
        os.makedirs(base_dir)
        
        sequences = [
            ("sequence1", 5, 120, 120),
            ("sequence2", 3, 80, 80),
            ("sequence3", 7, 150, 150)
        ]
        
        for name, frames, width, height in sequences:
            seq_dir = os.path.join(base_dir, name)
            create_test_png_sequence(seq_dir, frames, width, height)
            
            info = get_png_sequence_info(seq_dir)
            print(f"Created {name}: {info['width']}x{info['height']}, "
                  f"{info['frame_count']} frames")
        
        print("\nBatch resizing all sequences to 64x64...")
        
        # Batch resize all sequences
        batch_resize_png_sequences(base_dir, 64, 64, backup=True, quality='bilinear')
        
        print("\nAfter batch resize:")
        for name, _, _, _ in sequences:
            seq_dir = os.path.join(base_dir, name)
            info = get_png_sequence_info(seq_dir)
            print(f"{name}: {info['width']}x{info['height']}, "
                  f"{info['frame_count']} frames")


def example_preserve_transparency():
    """Example 5: Verify transparency preservation"""
    print("\n=== Example 5: Transparency preservation ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "transparent_sequence")
        
        # Create PNG sequence with complex transparency
        os.makedirs(png_dir, exist_ok=True)
        
        for i in range(3):
            # Create image with gradient transparency
            img = Image.new('RGBA', (60, 60), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw gradient with varying transparency
            for y in range(60):
                alpha = int(255 * (y / 60))
                color = (255, 100, 100, alpha)
                draw.line([(0, y), (60, y)], fill=color)
            
            # Add some opaque elements
            draw.rectangle([10, 10, 50, 20], fill=(0, 255, 0, 255))
            
            filename = f"frame_{i:06d}.png"
            img.save(os.path.join(png_dir, filename))
        
        print("Created PNG sequence with complex transparency")
        
        # Check original transparency
        original_file = os.path.join(png_dir, "frame_000000.png")
        original_img = Image.open(original_file)
        original_has_alpha = original_img.mode in ('RGBA', 'LA') or 'transparency' in original_img.info
        print(f"Original has transparency: {original_has_alpha}")
        original_img.close()
        
        # Resize
        resize_png_sequence_files(png_dir, 30, 30, backup=False, quality='lanczos')
        
        # Check resized transparency
        resized_img = Image.open(original_file)
        resized_has_alpha = resized_img.mode in ('RGBA', 'LA') or 'transparency' in resized_img.info
        print(f"Resized has transparency: {resized_has_alpha}")
        print(f"Resized mode: {resized_img.mode}")
        resized_img.close()


def example_error_handling():
    """Example 6: Error handling scenarios"""
    print("\n=== Example 6: Error handling ===")
    
    # Test non-existent folder
    try:
        resize_png_sequence_files("non_existent_folder", 100, 100)
    except IOError as e:
        print(f"Expected error for non-existent folder: {e}")
    
    # Test folder with no PNG files
    with tempfile.TemporaryDirectory() as temp_dir:
        empty_dir = os.path.join(temp_dir, "empty")
        os.makedirs(empty_dir)
        
        try:
            resize_png_sequence_files(empty_dir, 100, 100)
        except IOError as e:
            print(f"Expected error for empty folder: {e}")
    
    # Test invalid quality parameter
    with tempfile.TemporaryDirectory() as temp_dir:
        png_dir = os.path.join(temp_dir, "test_sequence")
        create_test_png_sequence(png_dir, 2, 50, 50)
        
        try:
            resize_png_sequence_files(png_dir, 100, 100, quality='invalid_quality')
        except ValueError as e:
            print(f"Expected error for invalid quality: {e}")


def main():
    """Run all examples"""
    print("PNG Sequence Resizer - Examples")
    print("=" * 50)
    
    example_basic_resize()
    example_resize_without_backup()
    example_different_quality_settings()
    example_batch_resize()
    example_preserve_transparency()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nUsage examples:")
    print("1. Resize single folder:")
    print("   python resize_png_sequence.py resize path/to/png_folder 320 240")
    print("2. Batch resize multiple folders:")
    print("   python resize_png_sequence.py batch path/to/base_folder 320 240")
    print("3. Get folder info:")
    print("   python resize_png_sequence.py info path/to/png_folder")


if __name__ == "__main__":
    main()
