<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>laboratory</artifactId>
        <groupId>org.example</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sf</artifactId>
    <packaging>jar</packaging>

    <name>sf</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sf</groupId>
            <artifactId>sf</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/Desktop/SF-CSIM-EXPRESS-SDK-V2.1.7.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.commons</groupId>
            <artifactId>logging</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/IdeaProjects/laboratory/sf/src/main/java/org/example/lib/commons-logging-1.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.commons</groupId>
            <artifactId>codec</artifactId>
            <version>1.9</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/IdeaProjects/laboratory/sf/src/main/java/org/example/lib/commons-codec-1.9.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.httpclient</groupId>
            <artifactId>httpclient</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/IdeaProjects/laboratory/sf/src/main/java/org/example/lib/httpclient-4.3.4.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.httpcore</groupId>
            <artifactId>httpcore</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/IdeaProjects/laboratory/sf/src/main/java/org/example/lib/httpcore-4.3.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.slf4j</groupId>
            <artifactId>slf4j</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>/Users/<USER>/IdeaProjects/laboratory/sf/src/main/java/org/example/lib/slf4j-api-1.7.7.jar</systemPath>
        </dependency>
    </dependencies>
</project>
